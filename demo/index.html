<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块游戏</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>俄罗斯方块</h1>
        </header>

        <main class="game-main">
            <section class="game-board-section">
                <div class="game-info">
                    <div class="score-display">
                        <h3>分数</h3>
                        <div id="score" class="score-value">0</div>
                    </div>
                    <div class="level-display">
                        <h3>等级</h3>
                        <div id="level" class="level-value">1</div>
                    </div>
                    <div class="lines-display">
                        <h3>已消除行数</h3>
                        <div id="lines" class="lines-value">0</div>
                    </div>
                </div>

                <div class="game-board-wrapper">
                    <canvas id="gameBoard" class="game-board" width="300" height="600"></canvas>
                </div>
            </section>

            <aside class="game-sidebar">
                <section class="next-piece-section">
                    <h3>下一个方块</h3>
                    <canvas id="nextPieceCanvas" class="next-piece" width="120" height="120"></canvas>
                </section>

                <section class="game-controls">
                    <h3>游戏控制</h3>
                    <div class="control-buttons">
                        <button id="startBtn" class="control-btn primary-btn">开始游戏</button>
                        <button id="pauseBtn" class="control-btn secondary-btn">暂停</button>
                        <button id="resetBtn" class="control-btn secondary-btn">重置</button>
                    </div>
                </section>

                <section class="instructions">
                    <h3>操作说明</h3>
                    <div class="instruction-list">
                        <div class="instruction-item">
                            <span class="key">←</span>
                            <span>左移</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">→</span>
                            <span>右移</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">↓</span>
                            <span>加速下落</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">↑</span>
                            <span>旋转</span>
                        </div>
                        <div class="instruction-item">
                            <span class="key">空格</span>
                            <span>直接落下</span>
                        </div>
                    </div>
                </section>

                <section class="game-stats">
                    <h3>游戏统计</h3>
                    <div class="stat-item">
                        <span>最高分:</span>
                        <span id="highScore">0</span>
                    </div>
                    <div class="stat-item">
                        <span>游戏时间:</span>
                        <span id="gameTime">00:00</span>
                    </div>
                </section>
            </aside>
        </main>

        <footer class="game-footer">
            <div class="game-status" id="gameStatus">准备开始</div>
        </footer>
    </div>

    <script src="game.js"></script>
</body>
</html>