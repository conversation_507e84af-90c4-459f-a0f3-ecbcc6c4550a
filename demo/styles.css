* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    max-width: 1200px;
    width: 100%;
    overflow: hidden;
}

.game-header {
    background: linear-gradient(45deg, #2c3e50, #3498db);
    color: white;
    text-align: center;
    padding: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.game-main {
    display: flex;
    padding: 20px;
    gap: 30px;
    flex-wrap: wrap;
}

.game-board-section {
    flex: 1;
    min-width: 350px;
}

.game-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.score-display, .level-display, .lines-display {
    background: linear-gradient(45deg, #34495e, #2c3e50);
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    text-align: center;
    min-width: 100px;
}

.score-display h3, .level-display h3, .lines-display h3 {
    font-size: 0.9em;
    margin-bottom: 5px;
    opacity: 0.8;
}

.score-value, .level-value, .lines-value {
    font-size: 1.8em;
    font-weight: bold;
    color: #f39c12;
}

.game-board-wrapper {
    background: #2c3e50;
    border-radius: 10px;
    padding: 10px;
    display: inline-block;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.5);
}

.game-board {
    border: 2px solid #34495e;
    border-radius: 5px;
    background: #1a252f;
    display: block;
}

.game-sidebar {
    width: 300px;
    min-width: 280px;
}

.next-piece-section {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.next-piece-section h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.next-piece {
    border: 2px solid #bdc3c7;
    border-radius: 5px;
    background: #34495e;
}

.game-controls {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.game-controls h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.control-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.control-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    font-size: 1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.primary-btn:hover {
    background: linear-gradient(45deg, #229954, #27ae60);
    transform: translateY(-2px);
}

.secondary-btn {
    background: linear-gradient(45deg, #95a5a6, #bdc3c7);
    color: #2c3e50;
}

.secondary-btn:hover {
    background: linear-gradient(45deg, #7f8c8d, #95a5a6);
    transform: translateY(-2px);
}

.control-btn:active {
    transform: translateY(0);
}

.instructions {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.instructions h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.instruction-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.instruction-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
}

.key {
    background: #34495e;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-weight: bold;
    min-width: 40px;
    text-align: center;
}

.game-stats {
    background: #ecf0f1;
    border-radius: 10px;
    padding: 20px;
}

.game-stats h3 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 5px 0;
    border-bottom: 1px solid #bdc3c7;
}

.stat-item:last-child {
    border-bottom: none;
}

.game-footer {
    background: #34495e;
    color: white;
    text-align: center;
    padding: 15px;
}

.game-status {
    font-size: 1.1em;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-main {
        flex-direction: column;
        align-items: center;
    }

    .game-sidebar {
        width: 100%;
        max-width: 400px;
    }

    .game-info {
        justify-content: center;
    }

    .game-header h1 {
        font-size: 2em;
    }

    .control-buttons {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .control-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .game-container {
        margin: 10px;
        border-radius: 15px;
    }

    .game-header h1 {
        font-size: 1.8em;
    }

    .game-main {
        padding: 15px;
        gap: 20px;
    }

    .game-board-section {
        min-width: auto;
    }

    .score-display, .level-display, .lines-display {
        min-width: 80px;
        padding: 10px 15px;
    }

    .score-value, .level-value, .lines-value {
        font-size: 1.5em;
    }
}

/* 游戏状态样式 */
.game-status.playing {
    color: #2ecc71;
}

.game-status.paused {
    color: #f39c12;
}

.game-status.game-over {
    color: #e74c3c;
}

.game-status.ready {
    color: #3498db;
}