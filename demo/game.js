// 游戏配置
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const BLOCK_SIZE = 30;
const NEXT_PIECE_SIZE = 30;

// 游戏状态
let gameState = {
    board: [],
    currentPiece: null,
    nextPiece: null,
    score: 0,
    level: 1,
    lines: 0,
    isPlaying: false,
    isPaused: false,
    gameOver: false,
    dropTime: 0,
    dropInterval: 1000
};

// 方块形状定义
const PIECES = [
    // I形状
    [
        [1, 1, 1, 1]
    ],
    // O形状
    [
        [1, 1],
        [1, 1]
    ],
    // T形状
    [
        [0, 1, 0],
        [1, 1, 1]
    ],
    // S形状
    [
        [0, 1, 1],
        [1, 1, 0]
    ],
    // Z形状
    [
        [1, 1, 0],
        [0, 1, 1]
    ],
    // J形状
    [
        [1, 0, 0],
        [1, 1, 1]
    ],
    // L形状
    [
        [0, 0, 1],
        [1, 1, 1]
    ]
];

// 方块颜色
const PIECE_COLORS = [
    '#00f0f0', // I - 青色
    '#f0f000', // O - 黄色
    '#a000f0', // T - 紫色
    '#00f000', // S - 绿色
    '#f00000', // Z - 红色
    '#0000f0', // J - 蓝色
    '#f0a000'  // L - 橙色
];

// 初始化游戏
function initGame() {
    const gameBoard = document.getElementById('gameBoard');
    const nextPieceCanvas = document.getElementById('nextPieceCanvas');

    // 设置画布尺寸
    gameBoard.width = BOARD_WIDTH * BLOCK_SIZE;
    gameBoard.height = BOARD_HEIGHT * BLOCK_SIZE;
    nextPieceCanvas.width = 4 * NEXT_PIECE_SIZE;
    nextPieceCanvas.height = 4 * NEXT_PIECE_SIZE;

    // 初始化游戏板
    gameState.board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));

    // 绑定事件
    bindEvents();

    // 开始游戏循环
    gameLoop();
}

// 调整颜色亮度
function adjustBrightness(color, factor) {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const newR = Math.max(0, Math.min(255, r + r * factor));
    const newG = Math.max(0, Math.min(255, g + g * factor));
    const newB = Math.max(0, Math.min(255, b + b * factor));

    return `rgb(${Math.floor(newR)}, ${Math.floor(newG)}, ${Math.floor(newB)})`;
}

// 创建新方块
function createPiece() {
    const type = Math.floor(Math.random() * PIECES.length);
    return {
        type: type,
        shape: PIECES[type],
        x: Math.floor(BOARD_WIDTH / 2) - Math.floor(PIECES[type][0].length / 2),
        y: 0,
        color: PIECE_COLORS[type]
    };
}

// 旋转方块
function rotatePiece(piece) {
    const rotated = piece.shape[0].map((_, index) =>
        piece.shape.map(row => row[index]).reverse()
    );
    return { ...piece, shape: rotated };
}

// 检查碰撞
function checkCollision(piece, board, offsetX = 0, offsetY = 0) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                const newX = piece.x + x + offsetX;
                const newY = piece.y + y + offsetY;

                if (newX < 0 || newX >= BOARD_WIDTH || newY >= BOARD_HEIGHT) {
                    return true;
                }

                if (newY >= 0 && board[newY][newX]) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 固定方块到游戏板
function lockPiece(piece, board) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                const boardY = piece.y + y;
                const boardX = piece.x + x;
                if (boardY >= 0) {
                    board[boardY][boardX] = piece.type + 1;
                }
            }
        }
    }
}

// 清除完整的行
function clearLines(board) {
    let linesCleared = 0;

    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
        if (board[y].every(cell => cell !== 0)) {
            board.splice(y, 1);
            board.unshift(Array(BOARD_WIDTH).fill(0));
            linesCleared++;
            y++; // 重新检查当前行
        }
    }

    return linesCleared;
}

// 更新分数
function updateScore(lines) {
    const linePoints = [0, 100, 300, 500, 800];
    gameState.score += linePoints[lines] * gameState.level;
    gameState.lines += lines;

    // 更新等级
    gameState.level = Math.floor(gameState.lines / 10) + 1;
    gameState.dropInterval = Math.max(100, 1000 - (gameState.level - 1) * 100);

    // 更新UI
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('level').textContent = gameState.level;
    document.getElementById('lines').textContent = gameState.lines;

    // 更新最高分
    const highScore = localStorage.getItem('tetrisHighScore') || 0;
    if (gameState.score > highScore) {
        localStorage.setItem('tetrisHighScore', gameState.score);
        document.getElementById('highScore').textContent = gameState.score;
    }
}

// 渲染游戏板
function renderBoard(board, ctx) {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // 绘制网格
    ctx.strokeStyle = '#2c3e50';
    ctx.lineWidth = 1;

    for (let x = 0; x <= BOARD_WIDTH; x++) {
        ctx.beginPath();
        ctx.moveTo(x * BLOCK_SIZE, 0);
        ctx.lineTo(x * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE);
        ctx.stroke();
    }

    for (let y = 0; y <= BOARD_HEIGHT; y++) {
        ctx.beginPath();
        ctx.moveTo(0, y * BLOCK_SIZE);
        ctx.lineTo(BOARD_WIDTH * BLOCK_SIZE, y * BLOCK_SIZE);
        ctx.stroke();
    }

    // 绘制已固定的方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (board[y][x]) {
                drawBlock(ctx, x, y, board[y][x] - 1);
            }
        }
    }
}

// 绘制方块
function drawBlock(ctx, x, y, type) {
    const gradient = ctx.createLinearGradient(
        x * BLOCK_SIZE, y * BLOCK_SIZE,
        (x + 1) * BLOCK_SIZE, (y + 1) * BLOCK_SIZE
    );
    gradient.addColorStop(0, PIECE_COLORS[type]);
    gradient.addColorStop(1, adjustBrightness(PIECE_COLORS[type], -0.3));

    ctx.fillStyle = gradient;
    ctx.fillRect(x * BLOCK_SIZE + 1, y * BLOCK_SIZE + 1, BLOCK_SIZE - 2, BLOCK_SIZE - 2);

    // 添加高光效果
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.fillRect(x * BLOCK_SIZE + 1, y * BLOCK_SIZE + 1, BLOCK_SIZE - 2, 4);
}

// 渲染下一个方块
function renderNextPiece(ctx, piece) {
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    if (piece) {
        const offsetX = Math.floor((4 - piece.shape[0].length) / 2);
        const offsetY = Math.floor((4 - piece.shape.length) / 2);

        for (let y = 0; y < piece.shape.length; y++) {
            for (let x = 0; x < piece.shape[y].length; x++) {
                if (piece.shape[y][x]) {
                    const drawX = x + offsetX;
                    const drawY = y + offsetY;
                    drawBlock(ctx, drawX, drawY, piece.type);
                }
            }
        }
    }
}

// 游戏主循环
function gameLoop(currentTime) {
    if (gameState.isPlaying && !gameState.isPaused) {
        if (currentTime - gameState.dropTime > gameState.dropInterval) {
            moveDown();
            gameState.dropTime = currentTime;
        }

        render();
    }

    requestAnimationFrame(gameLoop);
}

// 移动方块
function moveDown() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, 0, 1)) {
        gameState.currentPiece.y++;
    } else if (gameState.currentPiece) {
        lockPiece(gameState.currentPiece, gameState.board);
        const lines = clearLines(gameState.board);
        updateScore(lines);

        gameState.currentPiece = gameState.nextPiece;
        gameState.nextPiece = createPiece();

        if (checkCollision(gameState.currentPiece, gameState.board)) {
            gameOver();
        }
    }
}

// 左移
function moveLeft() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, -1, 0)) {
        gameState.currentPiece.x--;
        render();
    }
}

// 右移
function moveRight() {
    if (gameState.currentPiece && !checkCollision(gameState.currentPiece, gameState.board, 1, 0)) {
        gameState.currentPiece.x++;
        render();
    }
}

// 旋转
function rotateCurrentPiece() {
    if (gameState.currentPiece) {
        const rotated = rotatePiece(gameState.currentPiece);
        if (!checkCollision(rotated, gameState.board)) {
            gameState.currentPiece = rotated;
            render();
        }
    }
}

// 快速下落
function hardDrop() {
    if (gameState.currentPiece) {
        while (!checkCollision(gameState.currentPiece, gameState.board, 0, 1)) {
            gameState.currentPiece.y++;
        }
        lockPiece(gameState.currentPiece, gameState.board);
        const lines = clearLines(gameState.board);
        updateScore(lines);

        gameState.currentPiece = gameState.nextPiece;
        gameState.nextPiece = createPiece();

        if (checkCollision(gameState.currentPiece, gameState.board)) {
            gameOver();
        }

        render();
    }
}

// 开始游戏
function startGame() {
    gameState.board = Array(BOARD_HEIGHT).fill().map(() => Array(BOARD_WIDTH).fill(0));
    gameState.currentPiece = createPiece();
    gameState.nextPiece = createPiece();
    gameState.score = 0;
    gameState.level = 1;
    gameState.lines = 0;
    gameState.isPlaying = true;
    gameState.isPaused = false;
    gameState.gameOver = false;
    gameState.dropTime = 0;
    gameState.dropInterval = 1000;

    // 更新UI
    document.getElementById('score').textContent = '0';
    document.getElementById('level').textContent = '1';
    document.getElementById('lines').textContent = '0';
    document.getElementById('gameStatus').textContent = '游戏进行中';
    document.getElementById('gameStatus').className = 'game-status playing';

    render();
}

// 暂停游戏
function pauseGame() {
    if (gameState.isPlaying && !gameState.gameOver) {
        gameState.isPaused = !gameState.isPaused;
        const statusEl = document.getElementById('gameStatus');
        if (gameState.isPaused) {
            statusEl.textContent = '游戏暂停';
            statusEl.className = 'game-status paused';
        } else {
            statusEl.textContent = '游戏进行中';
            statusEl.className = 'game-status playing';
        }
    }
}

// 重置游戏
function resetGame() {
    gameState.isPlaying = false;
    gameState.isPaused = false;
    gameState.gameOver = false;

    const statusEl = document.getElementById('gameStatus');
    statusEl.textContent = '准备开始';
    statusEl.className = 'game-status ready';

    const ctx = document.getElementById('gameBoard').getContext('2d');
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    const nextCtx = document.getElementById('nextPieceCanvas').getContext('2d');
    nextCtx.clearRect(0, 0, nextCtx.canvas.width, nextCtx.canvas.height);
}

// 游戏结束
function gameOver() {
    gameState.gameOver = true;
    gameState.isPlaying = false;

    const statusEl = document.getElementById('gameStatus');
    statusEl.textContent = `游戏结束 - 最终分数: ${gameState.score}`;
    statusEl.className = 'game-status game-over';
}

// 渲染游戏
function render() {
    const ctx = document.getElementById('gameBoard').getContext('2d');
    const nextCtx = document.getElementById('nextPieceCanvas').getContext('2d');

    // 复制当前游戏板状态
    const displayBoard = gameState.board.map(row => [...row]);

    // 绘制当前方块
    if (gameState.currentPiece) {
        for (let y = 0; y < gameState.currentPiece.shape.length; y++) {
            for (let x = 0; x < gameState.currentPiece.shape[y].length; x++) {
                if (gameState.currentPiece.shape[y][x]) {
                    const boardY = gameState.currentPiece.y + y;
                    const boardX = gameState.currentPiece.x + x;
                    if (boardY >= 0) {
                        displayBoard[boardY][boardX] = gameState.currentPiece.type + 1;
                    }
                }
            }
        }
    }

    renderBoard(displayBoard, ctx);
    renderNextPiece(nextCtx, gameState.nextPiece);
}

// 绑定事件监听器
function bindEvents() {
    // 键盘事件
    document.addEventListener('keydown', (e) => {
        if (!gameState.isPlaying || gameState.gameOver) return;

        switch (e.key) {
            case 'ArrowLeft':
                e.preventDefault();
                moveLeft();
                break;
            case 'ArrowRight':
                e.preventDefault();
                moveRight();
                break;
            case 'ArrowDown':
                e.preventDefault();
                moveDown();
                break;
            case 'ArrowUp':
                e.preventDefault();
                rotateCurrentPiece();
                break;
            case ' ':
                e.preventDefault();
                hardDrop();
                break;
            case 'p':
            case 'P':
                e.preventDefault();
                pauseGame();
                break;
        }
    });

    // 按钮事件
    document.getElementById('startBtn').addEventListener('click', startGame);
    document.getElementById('pauseBtn').addEventListener('click', pauseGame);
    document.getElementById('resetBtn').addEventListener('click', resetGame);

    // 触摸事件支持
    let touchStartX = 0;
    let touchStartY = 0;

    document.getElementById('gameBoard').addEventListener('touchstart', (e) => {
        touchStartX = e.touches[0].clientX;
        touchStartY = e.touches[0].clientY;
    });

    document.getElementById('gameBoard').addEventListener('touchend', (e) => {
        if (!gameState.isPlaying || gameState.gameOver) return;

        const touchEndX = e.changedTouches[0].clientX;
        const touchEndY = e.changedTouches[0].clientY;

        const deltaX = touchEndX - touchStartX;
        const deltaY = touchEndY - touchStartY;

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            if (deltaX > 30) {
                moveRight();
            } else if (deltaX < -30) {
                moveLeft();
            }
        } else {
            if (deltaY > 30) {
                moveDown();
            }
        }
    });
}

// 游戏时间计时器
function updateGameTime() {
    if (gameState.isPlaying && !gameState.isPaused && !gameState.gameOver) {
        const startTime = localStorage.getItem('gameStartTime');
        if (!startTime) {
            localStorage.setItem('gameStartTime', Date.now().toString());
        } else {
            const elapsed = Math.floor((Date.now() - parseInt(startTime)) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('gameTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
}

// 页面加载完成后初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    // 加载最高分
    const highScore = localStorage.getItem('tetrisHighScore') || 0;
    document.getElementById('highScore').textContent = highScore;

    // 初始化游戏
    initGame();

    // 启动游戏时间计时器
    setInterval(updateGameTime, 1000);
});

// 导出游戏对象供外部调用
window.tetrisGame = {
    start: startGame,
    pause: pauseGame,
    reset: resetGame,
    moveLeft: moveLeft,
    moveRight: moveRight,
    rotate: rotateCurrentPiece,
    hardDrop: hardDrop
};