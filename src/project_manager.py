import os
import json
import uuid
import shutil
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional

from task_manager import TaskManager
from log_manager import TaskLogManager

class Project:
    """项目类"""
    def __init__(self, project_id: str, name: str, work_dir: str, description: str = "", requirement: str = "", provider: str = "local", rules_constraint: str = "", task_type: str = "新功能"):
        self.project_id = project_id
        self.name = name
        self.work_dir = work_dir
        self.description = description
        self.requirement = requirement  # 项目需求（代码格式）
        self.provider = provider  # LLM provider: local, zhipu, claude
        self.rules_constraint = rules_constraint  # 规则约束
        self.task_type = task_type  # 任务类型：代码重构、PMO需求、新功能、代码分析、其他
        self.created_at = datetime.now().isoformat()
        self.updated_at = self.created_at
        self.tasks_generated = False  # 是否已生成任务
        self.task_manager_session = None  # TaskManager会话ID
        self.run_state = False
    
    @property
    def directory(self) -> str:
        """获取项目目录路径"""
        return self.work_dir

    def to_dict(self) -> Dict[str, Any]:
        return {
            "project_id": self.project_id,
            "name": self.name,
            "work_dir": self.work_dir,
            "description": self.description,
            "requirement": self.requirement,
            "provider": self.provider,
            "rules_constraint": self.rules_constraint,  # 添加规则约束属性
            "task_type": self.task_type,  # 添加任务类型属性
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "tasks_generated": self.tasks_generated,
            "task_manager_session": self.task_manager_session
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Project':
        project = cls(
            project_id=data["project_id"],
            name=data["name"],
            work_dir=data["work_dir"],
            description=data.get("description", ""),
            requirement=data.get("requirement", ""),
            provider=data.get("provider", "local"),
            rules_constraint=data.get("rules_constraint", ""),  # 添加规则约束属性
            task_type=data.get("task_type", "新功能")  # 添加任务类型属性
        )
        project.created_at = data.get("created_at", project.created_at)
        project.updated_at = data.get("updated_at", project.updated_at)
        project.tasks_generated = data.get("tasks_generated", False)
        project.task_manager_session = data.get("task_manager_session", None)
        return project



class ProjectManager:
    """项目管理器"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = data_dir
        self.projects_file = os.path.join(data_dir, "projects.json")
        self.task_managers = {}

        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)

        # 初始化数据文件
        self._init_data_files()

        # 加载数据
        self.projects = self._load_projects()
        self.log_manager = TaskLogManager(data_dir)
    
    def _init_data_files(self):
        """初始化数据文件"""
        if not os.path.exists(self.projects_file):
            with open(self.projects_file, 'w', encoding='utf-8') as f:
                json.dump({"projects": []}, f, indent=2, ensure_ascii=False)
    
    def _load_projects(self) -> Dict[str, Project]:
        """加载项目数据"""
        try:
            with open(self.projects_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            projects = {}
            for project_data in data.get("projects", []):
                project = Project.from_dict(project_data)
                projects[project.project_id] = project
            
            return projects
        except Exception as e:
            print(f"加载项目数据失败: {e}")
            return {}
    

    
    def _save_projects(self):
        """保存项目数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.projects_file), exist_ok=True)

            data = {
                "projects": [project.to_dict() for project in self.projects.values()],
                "meta": {
                    "total_projects": len(self.projects),
                    "last_updated": datetime.now().isoformat()
                }
            }
            with open(self.projects_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存项目数据失败: {e}")
    
    def create_project(self, name: str, work_dir: str, description: str = "", requirement: str = "", provider: str = "local", task_type: str = "新功能", rules_constraint: str = "") -> str:
        """创建新项目"""
        # 使用时间戳作为项目ID而不是UUID
        import time
        project_id = str(int(time.time() * 1000))

        # 确保工作目录存在
        os.makedirs(work_dir, exist_ok=True)

        project = Project(project_id, name, work_dir, description, requirement, provider, rules_constraint, task_type=task_type)
        self.projects[project_id] = project

        self._save_projects()
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Project]:
        """获取项目"""
        return self.projects.get(project_id)
    
    def list_projects(self) -> List[Project]:
        """列出所有项目"""
        self._load_projects()
        return list(self.projects.values())
    
    def update_project(self, project_id: str, name: str = None, description: str = None, work_dir: str = None, requirement: str = None, provider: str = None, rules_constraint:str = None, task_type: str = None) -> bool:
        """更新项目"""
        project = self.projects.get(project_id)
        if not project:
            return False

        if name is not None:
            project.name = name
        if description is not None:
            project.description = description
        if work_dir is not None:
            project.work_dir = work_dir
        if requirement is not None:
            project.requirement = requirement
        if provider is not None:
            project.provider = provider
        if rules_constraint:
            project.rules_constraint = rules_constraint
        if task_type is not None:
            project.task_type = task_type

        project.updated_at = datetime.now().isoformat()
        self._save_projects()
        
        task_manager = self.task_managers.get(project_id)
        if task_manager:
            self.task_managers.pop(project_id)
            
        return True
    
    def delete_project(self, project_id: str, delete_files: bool = False) -> bool:
        """删除项目"""
        project = self.projects.get(project_id)
        if not project:
            return False
        
        task_manager = self.task_managers.get(project_id)
        if task_manager.is_running():
            logging.warning("项目正在运行，请先停止运行项目")
            return False
        task_manager.delete_all()
        
        # 删除项目文件夹（如果指定）
        if delete_files and os.path.exists(project.work_dir):
            try:
                import shutil
                shutil.rmtree(project.work_dir)
            except Exception as e:
                print(f"删除项目文件夹失败: {e}")

        # 删除项目记录
        del self.projects[project_id]
        self._save_projects()
        return True

    # 项目任务管理方法
    def get_task_manager(self, project_id: str) -> Optional[TaskManager]:
        """为项目创建TaskManager实例"""
        project = self.projects.get(project_id)
        if not project:
            return None

        task_manager = self.task_managers.get(project_id)
        if not task_manager:
            # 创建TaskManager实例，传入provider和task_type参数
            task_manager = TaskManager(project.name, project.work_dir, project.rules_constraint, self.log_manager, project.provider, project.task_type)
            self.task_managers[project_id] = task_manager
        else:
            # 更新TaskManager的provider
            task_manager.provider = project.provider
            task_manager.task_type = project.task_type
        return task_manager

    def generate_tasks_for_project(self, project_id: str, num_tasks: int = None) -> Dict[str, Any]:
        """为项目生成任务"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        if not project.requirement.strip():
            return {"success": False, "message": "项目需求为空，请先设置项目需求"}

        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法创建TaskManager"}

        # 生成任务
        user_req = f"""
        项目名称: {project.name}
        项目描述: {project.description}
        项目需求: {project.requirement}
        """

        result = task_manager.gen_tasks(user_req, num_tasks)

        if result.get("success"):
            # 更新项目状态
            project.tasks_generated = True
            project.task_manager_session = task_manager.session_id
            project.updated_at = datetime.now().isoformat()
            self._save_projects()

        return result

    def run_project_tasks(self, project_id: str, parallel_mode: bool = False) -> Dict[str, Any]:
        """运行项目的任务"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        if not project.tasks_generated:
            return {"success": False, "message": "项目尚未生成任务"}

        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法创建TaskManager"}

        # 运行任务
        result = task_manager.auto_run_tasks(parallel_mode=parallel_mode)

        if result.get("success"):
            # 更新项目状态
            project.updated_at = datetime.now().isoformat()
            self._save_projects()

        return result

    def stop_project_execution(self, project_id: str) -> Dict[str, Any]:
        """停止项目任务执行"""
        project = self.projects.get(project_id)
        if not project:
            return {"success": False, "message": "项目不存在"}

        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {"success": False, "message": "无法创建TaskManager"}

        # 停止任务执行
        try:
            task_manager.stop_execution()
            return {"success": True, "message": "已发送停止执行命令"}
        except Exception as e:
            return {"success": False, "message": f"停止执行失败: {str(e)}"}

    def get_project_summary(self, project_id: str) -> Dict[str, Any]:
        """获取项目摘要"""
        project = self.projects.get(project_id)
        if not project:
            return {}

        summary = {
            "project_id": project_id,
            "name": project.name,
            "description": project.description,
            "requirement": project.requirement,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "tasks_generated": project.tasks_generated,
            "total_tasks": 0,
            "completed_tasks": 0
        }

        # 统计任务数量（如果有TaskManager）
        if project.tasks_generated:
            task_manager = self.get_task_manager(project_id)
            if task_manager and os.path.exists(task_manager.task_file_name):
                try:
                    with open(task_manager.task_file_name, 'r', encoding='utf-8') as f:
                        tasks_data = json.load(f)
                    tasks = tasks_data.get("tasks", [])
                    summary["total_tasks"] = len(tasks)
                    summary["completed_tasks"] = len([t for t in tasks if t.get("status") == "completed"])
                except:
                    pass

        return summary

    def run_single_task(self, project_id: str, task_id: str) -> Dict[str, Any]:
        """运行项目中的单个任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {'success': False, 'message': '无法获取TaskManager'}

            # 创建进度回调函数
            logs = []
            def progress_callback(message):
                logs.append(f"{datetime.now().strftime('%H:%M:%S')} - {message}")

            result = task_manager.run_single_task(task_id, progress_callback)
            result['logs'] = logs
            return result

        except Exception as e:
            return {'success': False, 'message': str(e)}

    def get_task_logs(self, project_id: str, task_id: str) -> List[str]:
        """获取项目任务的运行日志"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return ["无法获取TaskManager"]

            return task_manager.get_task_logs(task_id)

        except Exception as e:
            return [f"获取日志失败: {e}"]

    def reset_project_tasks(self, project_id: str) -> dict[str, Any]:
        """重置项目的所有任务"""
        task_manager = self.get_task_manager(project_id)
        if not task_manager:
            return {
                "success": False,
                "message": "无法获取TaskManager"
            }

        return task_manager.reset_all_tasks()

    def add_task_to_project(self, project_id: str, title: str, description: str, priority: str = "medium", dependencies: List[str] = None) -> Dict[str, Any]:
        """为项目添加新任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {'success': False, 'message': '无法获取TaskManager'}

            task_id = task_manager.add_task(title, description, priority, dependencies)
            return {'success': True, 'task_id': task_id}

        except Exception as e:
            return {'success': False, 'message': str(e)}

    def update_project_task(self, project_id: str, task_id: int, **kwargs) -> Dict[str, Any]:
        """更新项目任务"""
        try:
            task_manager = self.get_task_manager(project_id)
            if not task_manager:
                return {'success': False, 'message': '无法获取TaskManager'}

            success = task_manager.update_task(task_id, **kwargs)
            if success:
                return {'success': True, 'message': '任务更新成功'}
            else:
                return {'success': False, 'message': '任务更新失败'}

        except Exception as e:
            return {'success': False, 'message': str(e)}
