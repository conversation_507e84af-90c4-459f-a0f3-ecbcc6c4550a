{% extends "base.html" %}

{% block title %}文件预览 - {{ project.name }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('project_detail', project_id=project.project_id) }}">{{ project.name }}</a></li>
                <li class="breadcrumb-item active">文件预览</li>
            </ol>
        </nav>
        <h1 class="h2">文件预览</h1>
        <p class="text-muted">{{ project.name }} - 项目文件浏览和预览</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="refreshFiles()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-outline-info" onclick="toggleView()">
                <i class="fas fa-th" id="view-toggle-icon"></i> <span id="view-toggle-text">网格视图</span>
            </button>
        </div>
    </div>
</div>

<!-- 文件路径导航 -->
<div class="card mb-3">
    <div class="card-body py-2">
        <nav aria-label="文件路径">
            <ol class="breadcrumb mb-0" id="file-breadcrumb">
                <li class="breadcrumb-item">
                    <a href="#" onclick="navigateToPath('')">
                        <i class="fas fa-home"></i> 根目录
                    </a>
                </li>
            </ol>
        </nav>
    </div>
</div>

<!-- 文件统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary" id="total-files">0</h5>
                <p class="card-text">总文件数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info" id="total-folders">0</h5>
                <p class="card-text">文件夹数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success" id="ai-generated-files">0</h5>
                <p class="card-text">AI生成文件</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning" id="total-size">0 KB</h5>
                <p class="card-text">总大小</p>
            </div>
        </div>
    </div>
</div>

<!-- 文件筛选和搜索 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <select class="form-select" id="file-type-filter" onchange="filterFiles()">
                    <option value="">所有文件类型</option>
                    <option value="text">文本文件</option>
                    <option value="code">代码文件</option>
                    <option value="image">图片文件</option>
                    <option value="document">文档文件</option>
                    <option value="data">数据文件</option>
                    <option value="other">其他文件</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="file-source-filter" onchange="filterFiles()">
                    <option value="">所有来源</option>
                    <option value="ai">AI生成</option>
                    <option value="user">用户创建</option>
                    <option value="system">系统文件</option>
                </select>
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" id="file-search" placeholder="搜索文件名..." onkeyup="searchFiles()">
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i> 清除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文件列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">文件列表</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-sort"></i> 排序
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="sortFiles('name')">按名称</a></li>
                <li><a class="dropdown-item" href="#" onclick="sortFiles('type')">按类型</a></li>
                <li><a class="dropdown-item" href="#" onclick="sortFiles('size')">按大小</a></li>
                <li><a class="dropdown-item" href="#" onclick="sortFiles('modified')">按修改时间</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        <!-- 列表视图 -->
        <div id="list-view">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>类型</th>
                            <th>大小</th>
                            <th>修改时间</th>
                            <th>来源</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="file-table-body">
                        <!-- 文件列表内容 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 网格视图 -->
        <div id="grid-view" style="display: none;">
            <div class="row" id="file-grid-container">
                <!-- 文件网格内容 -->
            </div>
        </div>
        
        <!-- 空状态 -->
        <div id="empty-state" class="text-center py-5" style="display: none;">
            <i class="fas fa-folder-open fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">文件夹为空</h3>
            <p class="text-muted">当前目录下没有文件</p>
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 文件预览模态框 -->
<div class="modal fade" id="filePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="preview-file-name">文件预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <!-- 文件内容预览区域 -->
                        <div id="file-preview-content">
                            <div class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">正在加载文件内容...</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <!-- 文件信息面板 -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">文件信息</h6>
                            </div>
                            <div class="card-body">
                                <div id="file-info-content">
                                    <!-- 文件信息内容 -->
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作面板 -->
                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">操作</h6>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="downloadFile()">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="copyPath()">
                                        <i class="fas fa-copy"></i> 复制路径
                                    </button>
                                    <button class="btn btn-outline-info" onclick="openInEditor()">
                                        <i class="fas fa-edit"></i> 编辑
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 文件信息模态框 -->
<div class="modal fade" id="fileInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">文件详细信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="detailed-file-info">
                <!-- 详细文件信息 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPath = '';
    let currentView = 'list';
    let allFiles = [];
    let filteredFiles = [];

    $(document).ready(function() {
        loadFiles(currentPath);
    });

    function loadFiles(path) {
        currentPath = path;

        // 显示加载状态
        showLoading();

        // 调用API获取文件列表
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/files`,
            method: 'GET',
            data: { path: path },
            success: function(response) {
                if (response.success) {
                    allFiles = response.files;
                    filteredFiles = [...allFiles];
                    updateFileDisplay();
                    updateStatistics();
                    updateBreadcrumb();
                } else {
                    showAlert('加载文件列表失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('加载文件列表失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                hideLoading();
            }
        });
    }

    function showLoading() {
        $('#file-table-body').html(`
            <tr>
                <td colspan="6" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">正在加载文件列表...</p>
                </td>
            </tr>
        `);
    }

    function hideLoading() {
        // 加载完成后会调用updateFileDisplay()更新显示
    }

    function updateFileDisplay() {
        if (filteredFiles.length === 0) {
            showEmptyState();
            return;
        }

        hideEmptyState();

        if (currentView === 'list') {
            updateListView();
        } else {
            updateGridView();
        }
    }

    function updateListView() {
        let html = '';

        filteredFiles.forEach(file => {
            const icon = getFileIcon(file);
            const sizeText = file.is_directory ? '-' : formatFileSize(file.size);
            const sourceText = getFileSource(file);
            const sourceBadge = getSourceBadge(sourceText);

            html += `
                <tr data-file-path="${file.path}" data-file-type="${file.type}" data-file-source="${sourceText}">
                    <td>
                        <div class="d-flex align-items-center">
                            <i class="${icon} me-2"></i>
                            <div>
                                ${file.is_directory ?
                                    `<a href="#" onclick="navigateToPath('${file.path}')" class="text-decoration-none">${file.name}</a>` :
                                    `<a href="#" onclick="previewFile('${file.path}')" class="text-decoration-none">${file.name}</a>`
                                }
                                ${file.is_ai_generated ? '<span class="badge bg-success ms-2">AI</span>' : ''}
                            </div>
                        </div>
                    </td>
                    <td>${getFileTypeText(file)}</td>
                    <td>${sizeText}</td>
                    <td>${formatDateTime(file.modified_time)}</td>
                    <td>${sourceBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            ${!file.is_directory ? `
                                <button class="btn btn-outline-primary" onclick="previewFile('${file.path}')" title="预览">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="downloadFile('${file.path}')" title="下载">
                                    <i class="fas fa-download"></i>
                                </button>
                            ` : `
                                <button class="btn btn-outline-info" onclick="navigateToPath('${file.path}')" title="打开">
                                    <i class="fas fa-folder-open"></i>
                                </button>
                            `}
                            <button class="btn btn-outline-info" onclick="showFileInfo('${file.path}')" title="信息">
                                <i class="fas fa-info"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        $('#file-table-body').html(html);
    }

    function updateGridView() {
        let html = '';

        filteredFiles.forEach(file => {
            const icon = getFileIcon(file);
            const sizeText = file.is_directory ? '' : formatFileSize(file.size);

            html += `
                <div class="col-lg-2 col-md-3 col-sm-4 col-6 mb-3">
                    <div class="card file-card h-100" data-file-path="${file.path}" data-file-type="${file.type}">
                        <div class="card-body text-center p-3">
                            <i class="${icon} fa-3x mb-2 text-primary"></i>
                            <h6 class="card-title small">${file.name}</h6>
                            ${file.is_ai_generated ? '<span class="badge bg-success mb-2">AI</span>' : ''}
                            ${sizeText ? `<p class="card-text small text-muted">${sizeText}</p>` : ''}
                            <div class="btn-group btn-group-sm">
                                ${!file.is_directory ? `
                                    <button class="btn btn-outline-primary btn-sm" onclick="previewFile('${file.path}')" title="预览">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                ` : `
                                    <button class="btn btn-outline-info btn-sm" onclick="navigateToPath('${file.path}')" title="打开">
                                        <i class="fas fa-folder-open"></i>
                                    </button>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        $('#file-grid-container').html(html);
    }

    function showEmptyState() {
        $('#list-view').hide();
        $('#grid-view').hide();
        $('#empty-state').show();
    }

    function hideEmptyState() {
        $('#empty-state').hide();
        if (currentView === 'list') {
            $('#list-view').show();
            $('#grid-view').hide();
        } else {
            $('#list-view').hide();
            $('#grid-view').show();
        }
    }

    function updateStatistics() {
        let totalFiles = 0;
        let totalFolders = 0;
        let aiGeneratedFiles = 0;
        let totalSize = 0;

        allFiles.forEach(file => {
            if (file.is_directory) {
                totalFolders++;
            } else {
                totalFiles++;
                totalSize += file.size || 0;
                if (file.is_ai_generated) {
                    aiGeneratedFiles++;
                }
            }
        });

        $('#total-files').text(totalFiles);
        $('#total-folders').text(totalFolders);
        $('#ai-generated-files').text(aiGeneratedFiles);
        $('#total-size').text(formatFileSize(totalSize));
    }

    function updateBreadcrumb() {
        let html = `
            <li class="breadcrumb-item">
                <a href="#" onclick="navigateToPath('')">
                    <i class="fas fa-home"></i> 根目录
                </a>
            </li>
        `;

        if (currentPath) {
            const pathParts = currentPath.split('/').filter(part => part);
            let buildPath = '';

            pathParts.forEach((part, index) => {
                buildPath += (buildPath ? '/' : '') + part;
                const isLast = index === pathParts.length - 1;

                if (isLast) {
                    html += `<li class="breadcrumb-item active">${part}</li>`;
                } else {
                    html += `<li class="breadcrumb-item"><a href="#" onclick="navigateToPath('${buildPath}')">${part}</a></li>`;
                }
            });
        }

        $('#file-breadcrumb').html(html);
    }

    function navigateToPath(path) {
        loadFiles(path);
    }

    function refreshFiles() {
        loadFiles(currentPath);
    }

    function toggleView() {
        if (currentView === 'list') {
            currentView = 'grid';
            $('#view-toggle-icon').removeClass('fa-th').addClass('fa-list');
            $('#view-toggle-text').text('列表视图');
        } else {
            currentView = 'list';
            $('#view-toggle-icon').removeClass('fa-list').addClass('fa-th');
            $('#view-toggle-text').text('网格视图');
        }
        updateFileDisplay();
    }

    function filterFiles() {
        const typeFilter = $('#file-type-filter').val();
        const sourceFilter = $('#file-source-filter').val();

        filteredFiles = allFiles.filter(file => {
            let typeMatch = true;
            let sourceMatch = true;

            if (typeFilter) {
                typeMatch = getFileCategory(file) === typeFilter;
            }

            if (sourceFilter) {
                sourceMatch = getFileSource(file) === sourceFilter;
            }

            return typeMatch && sourceMatch;
        });

        updateFileDisplay();
    }

    function searchFiles() {
        const searchTerm = $('#file-search').val().toLowerCase();

        if (!searchTerm) {
            filterFiles(); // 重新应用其他筛选器
            return;
        }

        filteredFiles = allFiles.filter(file => {
            return file.name.toLowerCase().includes(searchTerm);
        });

        updateFileDisplay();
    }

    function clearFilters() {
        $('#file-type-filter').val('');
        $('#file-source-filter').val('');
        $('#file-search').val('');
        filteredFiles = [...allFiles];
        updateFileDisplay();
    }

    function sortFiles(sortBy) {
        filteredFiles.sort((a, b) => {
            switch (sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'type':
                    return getFileTypeText(a).localeCompare(getFileTypeText(b));
                case 'size':
                    return (a.size || 0) - (b.size || 0);
                case 'modified':
                    return new Date(a.modified_time) - new Date(b.modified_time);
                default:
                    return 0;
            }
        });
        updateFileDisplay();
    }

    function previewFile(filePath) {
        $('#preview-file-name').text(filePath.split('/').pop());
        $('#filePreviewModal').modal('show');

        // 显示加载状态
        $('#file-preview-content').html(`
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">正在加载文件内容...</p>
            </div>
        `);

        // 获取文件内容
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/files/preview`,
            method: 'GET',
            data: { path: filePath },
            success: function(response) {
                if (response.success) {
                    displayFileContent(response.content, response.file_type);
                    displayFileInfo(response.file_info);
                } else {
                    $('#file-preview-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i>
                            预览失败: ${response.message}
                        </div>
                    `);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                $('#file-preview-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        预览失败: ${response.message || '未知错误'}
                    </div>
                `);
            }
        });
    }

    function displayFileContent(content, fileType) {
        let html = '';

        if (fileType === 'image') {
            html = `<img src="data:image/png;base64,${content}" class="img-fluid" alt="图片预览">`;
        } else if (fileType === 'text' || fileType === 'code') {
            html = `
                <div class="code-preview">
                    <pre><code class="language-${getLanguageFromPath(currentPath)}">${escapeHtml(content)}</code></pre>
                </div>
            `;
        } else if (fileType === 'json') {
            try {
                const jsonObj = JSON.parse(content);
                html = `
                    <div class="json-preview">
                        <pre><code class="language-json">${JSON.stringify(jsonObj, null, 2)}</code></pre>
                    </div>
                `;
            } catch (e) {
                html = `
                    <div class="text-preview">
                        <pre><code>${escapeHtml(content)}</code></pre>
                    </div>
                `;
            }
        } else {
            html = `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    此文件类型不支持在线预览，请下载后查看。
                </div>
            `;
        }

        $('#file-preview-content').html(html);

        // 如果有代码高亮库，应用高亮
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    }

    function displayFileInfo(fileInfo) {
        const html = `
            <div class="file-info">
                <div class="mb-2">
                    <strong>文件名:</strong><br>
                    <span class="text-muted">${fileInfo.name}</span>
                </div>
                <div class="mb-2">
                    <strong>文件大小:</strong><br>
                    <span class="text-muted">${formatFileSize(fileInfo.size)}</span>
                </div>
                <div class="mb-2">
                    <strong>文件类型:</strong><br>
                    <span class="text-muted">${fileInfo.type}</span>
                </div>
                <div class="mb-2">
                    <strong>修改时间:</strong><br>
                    <span class="text-muted">${formatDateTime(fileInfo.modified_time)}</span>
                </div>
                <div class="mb-2">
                    <strong>文件路径:</strong><br>
                    <span class="text-muted small">${fileInfo.path}</span>
                </div>
                ${fileInfo.is_ai_generated ? `
                    <div class="mb-2">
                        <span class="badge bg-success">AI生成文件</span>
                    </div>
                ` : ''}
            </div>
        `;

        $('#file-info-content').html(html);
    }

    function showFileInfo(filePath) {
        // 显示文件详细信息
        showAlert('文件详细信息功能正在开发中...', 'info');
    }

    function downloadFile(filePath) {
        // 下载文件
        window.open(`/api/projects/{{ project.project_id }}/files/download?path=${encodeURIComponent(filePath)}`, '_blank');
    }

    function copyPath() {
        // 复制文件路径
        showAlert('复制路径功能正在开发中...', 'info');
    }

    function openInEditor() {
        // 在编辑器中打开
        showAlert('编辑器功能正在开发中...', 'info');
    }

    // 工具函数
    function getFileIcon(file) {
        if (file.is_directory) {
            return 'fas fa-folder text-warning';
        }

        const ext = file.name.split('.').pop().toLowerCase();

        // 代码文件
        if (['py', 'js', 'html', 'css', 'java', 'cpp', 'c', 'php', 'rb', 'go'].includes(ext)) {
            return 'fas fa-code text-info';
        }

        // 图片文件
        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
            return 'fas fa-image text-success';
        }

        // 文档文件
        if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
            return 'fas fa-file-alt text-primary';
        }

        // 数据文件
        if (['json', 'xml', 'csv', 'xlsx'].includes(ext)) {
            return 'fas fa-database text-warning';
        }

        return 'fas fa-file text-muted';
    }

    function getFileTypeText(file) {
        if (file.is_directory) {
            return '文件夹';
        }

        const ext = file.name.split('.').pop().toLowerCase();

        const typeMap = {
            'py': 'Python文件',
            'js': 'JavaScript文件',
            'html': 'HTML文件',
            'css': 'CSS文件',
            'json': 'JSON文件',
            'txt': '文本文件',
            'md': 'Markdown文件',
            'jpg': '图片文件',
            'jpeg': '图片文件',
            'png': '图片文件',
            'gif': '图片文件',
            'pdf': 'PDF文档',
            'doc': 'Word文档',
            'docx': 'Word文档'
        };

        return typeMap[ext] || `${ext.toUpperCase()}文件`;
    }

    function getFileCategory(file) {
        if (file.is_directory) {
            return 'folder';
        }

        const ext = file.name.split('.').pop().toLowerCase();

        if (['py', 'js', 'html', 'css', 'java', 'cpp', 'c', 'php', 'rb', 'go'].includes(ext)) {
            return 'code';
        }

        if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(ext)) {
            return 'image';
        }

        if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
            return 'document';
        }

        if (['json', 'xml', 'csv', 'xlsx'].includes(ext)) {
            return 'data';
        }

        return 'other';
    }

    function getFileSource(file) {
        if (file.is_ai_generated) {
            return 'ai';
        }

        // 简单判断：系统文件通常在特定目录下
        if (file.path.includes('node_modules') || file.path.includes('.git') || file.path.includes('__pycache__')) {
            return 'system';
        }

        return 'user';
    }

    function getSourceBadge(source) {
        const badgeMap = {
            'ai': '<span class="badge bg-success">AI生成</span>',
            'user': '<span class="badge bg-primary">用户创建</span>',
            'system': '<span class="badge bg-secondary">系统文件</span>'
        };

        return badgeMap[source] || '<span class="badge bg-light text-dark">未知</span>';
    }

    function getLanguageFromPath(path) {
        const ext = path.split('.').pop().toLowerCase();

        const langMap = {
            'py': 'python',
            'js': 'javascript',
            'html': 'html',
            'css': 'css',
            'json': 'json',
            'md': 'markdown',
            'java': 'java',
            'cpp': 'cpp',
            'c': 'c',
            'php': 'php',
            'rb': 'ruby',
            'go': 'go'
        };

        return langMap[ext] || 'text';
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';

        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function formatDateTime(dateString) {
        if (!dateString) return '-';

        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
</script>
{% endblock %}
