{% extends "base.html" %}

{% block title %}任务管理 - {{ requirement.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('project_detail', project_id=project.project_id) }}">{{ project.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('requirement_detail', req_id=requirement.req_id) }}">{{ requirement.title }}</a></li>
                <li class="breadcrumb-item active">任务管理</li>
            </ol>
        </nav>
        <h1 class="h2">任务管理</h1>
        <p class="text-muted">{{ requirement.title }} - 任务详细管理</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="addTask()">
                <i class="fas fa-plus"></i> 新增任务
            </button>
            <button type="button" class="btn btn-success" onclick="generateTasks()">
                <i class="fas fa-cogs"></i> AI生成任务
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 任务统计 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ task_stats.total }}</h5>
                <p class="card-text">总任务数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-secondary">{{ task_stats.pending }}</h5>
                <p class="card-text">待处理</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ task_stats.running }}</h5>
                <p class="card-text">运行中</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ task_stats.completed }}</h5>
                <p class="card-text">已完成</p>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">任务列表</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-filter"></i> 筛选
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="filterTasks('all')">全部</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTasks('pending')">待处理</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTasks('running')">运行中</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTasks('completed')">已完成</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterTasks('failed')">失败</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        {% if tasks %}
            <div class="table-responsive">
                <table class="table table-hover" id="tasks-table">
                    <thead>
                        <tr>
                            <th width="5%">
                                <input type="checkbox" id="select-all" onchange="toggleSelectAll()">
                            </th>
                            <th width="5%">ID</th>
                            <th width="25%">标题</th>
                            <th width="15%">状态</th>
                            <th width="10%">优先级</th>
                            <th width="15%">依赖</th>
                            <th width="10%">进度</th>
                            <th width="15%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr data-task-id="{{ task.id }}" data-status="{{ task.status }}">
                            <td>
                                <input type="checkbox" class="task-checkbox" value="{{ task.id }}"
                                       {% if task.status == 'pending' %}{% else %}disabled{% endif %}>
                            </td>
                            <td>{{ task.id }}</td>
                            <td>
                                <div>
                                    <strong>{{ task.title }}</strong>
                                    <br><small class="text-muted">{{ task.description[:80] }}{% if task.description|length > 80 %}...{% endif %}</small>
                                </div>
                            </td>
                            <td>{{ getStatusBadge(task.status)|safe }}</td>
                            <td>{{ getPriorityBadge(task.priority)|safe }}</td>
                            <td>
                                {% if task.dependencies %}
                                    {% for dep in task.dependencies %}
                                        <span class="badge bg-info">{{ dep }}</span>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">无</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if task.status == 'completed' %}
                                    <div class="progress">
                                        <div class="progress-bar bg-success" style="width: 100%">100%</div>
                                    </div>
                                {% elif task.status == 'running' %}
                                    <div class="progress">
                                        <div class="progress-bar bg-info progress-bar-striped progress-bar-animated" style="width: 50%">50%</div>
                                    </div>
                                {% elif task.status == 'failed' %}
                                    <div class="progress">
                                        <div class="progress-bar bg-danger" style="width: 100%">失败</div>
                                    </div>
                                {% else %}
                                    <div class="progress">
                                        <div class="progress-bar bg-secondary" style="width: 0%">0%</div>
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewTask({{ task.id }})" title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    {% if task.status == 'pending' %}
                                    <button class="btn btn-outline-warning" onclick="editTask({{ task.id }})" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-outline-danger" onclick="deleteTask({{ task.id }})" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 批量操作 -->
            <div class="mt-3">
                <div class="btn-group" id="batch-actions" style="display: none;">
                    <button class="btn btn-outline-danger" onclick="batchDeleteTasks()">
                        <i class="fas fa-trash"></i> 批量删除
                    </button>
                    <button class="btn btn-outline-warning" onclick="batchResetTasks()">
                        <i class="fas fa-undo"></i> 批量重置
                    </button>
                </div>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tasks fa-5x text-muted mb-4"></i>
                <h3 class="text-muted">还没有任务</h3>
                <p class="text-muted mb-4">为这个需求创建或生成任务</p>
                <div class="btn-group">
                    <button class="btn btn-primary" onclick="addTask()">
                        <i class="fas fa-plus"></i> 新增任务
                    </button>
                    <button class="btn btn-success" onclick="generateTasks()">
                        <i class="fas fa-cogs"></i> AI生成任务
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 新增/编辑任务模态框 -->
<div class="modal fade" id="taskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskModalTitle">新增任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="taskForm">
                    <input type="hidden" id="taskId">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="taskTitle" class="form-label">任务标题</label>
                                <input type="text" class="form-control" id="taskTitle" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="taskPriority" class="form-label">优先级</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="high">高</option>
                                    <option value="medium" selected>中</option>
                                    <option value="low">低</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" rows="3" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskDetails" class="form-label">详细信息</label>
                        <textarea class="form-control" id="taskDetails" rows="4" 
                                  placeholder="任务的详细实现步骤和要求"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskDependencies" class="form-label">依赖任务</label>
                                <select class="form-select" id="taskDependencies" multiple>
                                    {% for task in tasks %}
                                        <option value="{{ task.id }}">{{ task.id }} - {{ task.title }}</option>
                                    {% endfor %}
                                </select>
                                <small class="text-muted">按住Ctrl键可选择多个依赖任务</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="taskTestStrategy" class="form-label">测试策略</label>
                                <input type="text" class="form-control" id="taskTestStrategy" 
                                       placeholder="如何验证任务完成">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveTask()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="task-detail-content">
                <!-- 任务详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- AI生成任务模态框 -->
<div class="modal fade" id="generateTasksModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">AI生成任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>为需求"{{ requirement.title }}"生成AI任务？</p>
                <div class="mb-3">
                    <label for="numTasks" class="form-label">任务数量（可选）</label>
                    <input type="number" class="form-control" id="numTasks" min="1" max="20" 
                           placeholder="留空让AI自动决定">
                </div>
                <div class="mb-3">
                    <label for="taskComplexity" class="form-label">任务复杂度</label>
                    <select class="form-select" id="taskComplexity">
                        <option value="simple">简单 - 基础任务</option>
                        <option value="medium" selected>中等 - 标准任务</option>
                        <option value="complex">复杂 - 详细任务</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGenerateTasks()">生成任务</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentFilter = 'all';

    $(document).ready(function() {
        // 监听复选框变化
        $('.task-checkbox').change(function() {
            updateBatchActions();
        });
    });

    function toggleSelectAll() {
        const selectAll = $('#select-all').prop('checked');
        $('.task-checkbox:not(:disabled)').prop('checked', selectAll);
        updateBatchActions();
    }

    function updateBatchActions() {
        const checkedCount = $('.task-checkbox:checked').length;
        if (checkedCount > 0) {
            $('#batch-actions').show();
        } else {
            $('#batch-actions').hide();
        }
    }

    function filterTasks(status) {
        currentFilter = status;
        const rows = $('#tasks-table tbody tr');

        rows.each(function() {
            const taskStatus = $(this).data('status');
            if (status === 'all' || taskStatus === status) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    function addTask() {
        $('#taskModalTitle').text('新增任务');
        $('#taskForm')[0].reset();
        $('#taskId').val('');
        $('#taskModal').modal('show');
    }

    function editTask(taskId) {
        // 获取任务信息
        const tasks = {{ tasks|tojson|safe }};
        const task = tasks.find(t => t.id === taskId);

        if (task) {
            $('#taskModalTitle').text('编辑任务');
            $('#taskId').val(task.id);
            $('#taskTitle').val(task.title);
            $('#taskDescription').val(task.description);
            $('#taskDetails').val(task.details || '');
            $('#taskPriority').val(task.priority);
            $('#taskTestStrategy').val(task.testStrategy || '');

            // 设置依赖
            $('#taskDependencies').val(task.dependencies || []);

            $('#taskModal').modal('show');
        }
    }

    function saveTask() {
        const taskId = $('#taskId').val();
        const title = $('#taskTitle').val().trim();
        const description = $('#taskDescription').val().trim();
        const details = $('#taskDetails').val().trim();
        const priority = $('#taskPriority').val();
        const dependencies = $('#taskDependencies').val() || [];
        const testStrategy = $('#taskTestStrategy').val().trim();

        if (!title || !description) {
            showAlert('请填写任务标题和描述', 'warning');
            return;
        }

        const taskData = {
            title: title,
            description: description,
            details: details,
            priority: priority,
            dependencies: dependencies,
            testStrategy: testStrategy
        };

        const url = taskId ? `/api/tasks/${taskId}` : '/api/tasks';
        const method = taskId ? 'PUT' : 'POST';

        if (!taskId) {
            taskData.req_id = '{{ requirement.req_id }}';
        }

        $.ajax({
            url: url,
            method: method,
            contentType: 'application/json',
            data: JSON.stringify(taskData),
            success: function(response) {
                if (response.success) {
                    showAlert(taskId ? '任务更新成功！' : '任务创建成功！', 'success');
                    $('#taskModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('操作失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('操作失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function viewTask(taskId) {
        const tasks = {{ tasks|tojson|safe }};
        const task = tasks.find(t => t.id === taskId);

        if (task) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>任务ID:</strong> ${task.id}</p>
                        <p><strong>标题:</strong> ${task.title}</p>
                        <p><strong>状态:</strong> ${getStatusBadge(task.status)}</p>
                        <p><strong>优先级:</strong> ${getPriorityBadge(task.priority)}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>依赖:</strong> ${task.dependencies && task.dependencies.length ? task.dependencies.join(', ') : '无'}</p>
                        <p><strong>测试策略:</strong> ${task.testStrategy || '无'}</p>
                        <p><strong>创建时间:</strong> ${task.created_at || '未知'}</p>
                        <p><strong>更新时间:</strong> ${task.updated_at || '未知'}</p>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>描述:</strong>
                    <p>${task.description}</p>
                </div>
                <div class="mb-3">
                    <strong>详细信息:</strong>
                    <pre class="bg-light p-3">${task.details || '无'}</pre>
                </div>
                ${task.result ? `
                <div class="mb-3">
                    <strong>执行结果:</strong>
                    <pre class="bg-light p-3">${task.result}</pre>
                </div>
                ` : ''}
                ${task.error ? `
                <div class="mb-3">
                    <strong>错误信息:</strong>
                    <pre class="bg-danger text-white p-3">${task.error}</pre>
                </div>
                ` : ''}
            `;

            $('#task-detail-content').html(content);
            $('#taskDetailModal').modal('show');
        }
    }

    function deleteTask(taskId) {
        // 检查是否有其他任务依赖此任务
        const tasks = {{ tasks|tojson|safe }};
        const dependentTasks = tasks.filter(t => t.dependencies && t.dependencies.includes(taskId));

        if (dependentTasks.length > 0) {
            const dependentTaskNames = dependentTasks.map(t => `${t.id} - ${t.title}`).join('\\n');
            showAlert(`无法删除任务 ${taskId}，以下任务依赖于它：\\n${dependentTaskNames}`, 'warning');
            return;
        }

        if (confirm(`确定要删除任务 ${taskId} 吗？此操作不可撤销。`)) {
            $.ajax({
                url: `/api/tasks/${taskId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务删除成功！', 'success');
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    } else {
                        showAlert('删除失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('删除失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function batchDeleteTasks() {
        const selectedTasks = $('.task-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedTasks.length === 0) {
            showAlert('请选择要删除的任务', 'warning');
            return;
        }

        if (confirm(`确定要删除选中的 ${selectedTasks.length} 个任务吗？此操作不可撤销。`)) {
            showAlert('批量删除功能正在开发中...', 'warning');
        }
    }

    function batchResetTasks() {
        const selectedTasks = $('.task-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedTasks.length === 0) {
            showAlert('请选择要重置的任务', 'warning');
            return;
        }

        if (confirm(`确定要重置选中的 ${selectedTasks.length} 个任务的状态吗？`)) {
            showAlert('批量重置功能正在开发中...', 'warning');
        }
    }

    function generateTasks() {
        $('#generateTasksModal').modal('show');
    }

    function confirmGenerateTasks() {
        const numTasks = $('#numTasks').val() || null;
        const complexity = $('#taskComplexity').val();

        showAlert('正在生成任务，请稍候...', 'info');
        $('#generateTasksModal').modal('hide');

        $.ajax({
            url: `/api/requirements/{{ requirement.req_id }}/generate_tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                num_tasks: numTasks ? parseInt(numTasks) : null,
                complexity: complexity
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('任务生成成功！', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('任务生成失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }
</script>
{% endblock %}
