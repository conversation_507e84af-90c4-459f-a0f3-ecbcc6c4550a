{% extends "base.html" %}

{% block title %}任务 {{ task_id }} - LLM交互日志 - AI任务管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <a href="{{ url_for('project_tasks', project_id=project.project_id) }}" class="text-decoration-none text-muted">
            <i class="fas fa-arrow-left"></i>
        </a>
        任务 {{ task_id }} - LLM交互日志
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="toggleAutoRefresh()">
                <i class="fas fa-play" id="autoRefreshIcon"></i> <span id="autoRefreshText">开启自动刷新</span>
            </button>
            <button type="button" class="btn btn-outline-info" onclick="clearLogs()">
                <i class="fas fa-trash"></i> 清空显示
            </button>
        </div>
    </div>
</div>

<!-- 日志统计信息 -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body py-2">
                <div class="row text-center">
                    <div class="col-md-3">
                        <small class="text-muted">总日志数</small>
                        <div class="h6 mb-0" id="totalLogsCount">0</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">最后更新</small>
                        <div class="h6 mb-0" id="lastUpdateTime">-</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">自动刷新</small>
                        <div class="h6 mb-0" id="autoRefreshStatus">关闭</div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">连接状态</small>
                        <div class="h6 mb-0" id="connectionStatus">连接中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志内容区域 -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-comments"></i> LLM交互日志</h6>
    </div>
    <div class="card-body p-0">
        <div id="logsContainer" class="p-3" style="max-height: 70vh; overflow-y: auto;">
            <div class="text-center text-muted py-5">
                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                <div>正在加载日志...</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let autoRefreshInterval = null;
let lastTimestamp = null;
let isAutoRefreshEnabled = false;

$(document).ready(function() {
    loadLogs();
});

function loadLogs(incremental = false) {
    const url = incremental && lastTimestamp 
        ? `/api/projects/{{ project.project_id }}/tasks/{{ task_id }}/llm-logs?since=${encodeURIComponent(lastTimestamp)}`
        : `/api/projects/{{ project.project_id }}/tasks/{{ task_id }}/llm-logs`;
    
    $.ajax({
        url: url,
        method: 'GET',
        success: function(response) {
            $('#connectionStatus').text('已连接').removeClass('text-danger').addClass('text-success');
            
            if (response.success && response.logs) {
                if (incremental && response.logs.length > 0) {
                    // 增量更新：追加新日志
                    appendLogs(response.logs);
                } else if (!incremental) {
                    // 全量更新：替换所有日志
                    renderLogs(response.logs);
                }
                
                // 更新统计信息
                updateStats(response.logs);
                
                // 更新最后时间戳
                if (response.logs.length > 0) {
                    lastTimestamp = response.logs[response.logs.length - 1].timestamp;
                }
            } else if (!incremental) {
                $('#logsContainer').html(`
                    <div class="text-center text-muted py-5">
                        <i class="fas fa-file-alt fa-2x mb-3"></i>
                        <div>暂无日志记录</div>
                    </div>
                `);
            }
        },
        error: function(xhr) {
            $('#connectionStatus').text('连接失败').removeClass('text-success').addClass('text-danger');
            
            if (!incremental) {
                const response = xhr.responseJSON || {};
                $('#logsContainer').html(`
                    <div class="text-center text-danger py-5">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <div>加载日志失败: ${response.error || '未知错误'}</div>
                        <button class="btn btn-outline-primary mt-3" onclick="loadLogs()">重试</button>
                    </div>
                `);
            }
        }
    });
}

function renderLogs(logs) {
    if (logs.length === 0) {
        $('#logsContainer').html(`
            <div class="text-center text-muted py-5">
                <i class="fas fa-file-alt fa-2x mb-3"></i>
                <div>暂无日志记录</div>
            </div>
        `);
        return;
    }

    let html = '';
    logs.forEach((log, index) => {
        html += createLogEntryHtml(log, index);
    });
    
    $('#logsContainer').html(html);
    
    // 滚动到底部
    const container = document.getElementById('logsContainer');
    container.scrollTop = container.scrollHeight;
}

function appendLogs(logs) {
    if (logs.length === 0) return;
    
    const container = $('#logsContainer');
    const currentCount = container.find('.log-entry').length;
    
    let html = '';
    logs.forEach((log, index) => {
        html += createLogEntryHtml(log, currentCount + index);
    });
    
    container.append(html);
    
    // 滚动到底部
    const containerElement = document.getElementById('logsContainer');
    containerElement.scrollTop = containerElement.scrollHeight;
}

function createLogEntryHtml(log, index) {
    const logTypeColors = {
        'Request': 'bg-light border-start border-primary border-3',
        'Assistant': 'bg-light border-start border-success border-3', 
        'User': 'bg-light border-start border-info border-3',
        'Result': 'bg-light border-start border-warning border-3',
        'Stream': 'bg-light border-start border-secondary border-3'
    };
    
    const logTypeIcons = {
        'Request': 'fas fa-paper-plane text-primary',
        'Assistant': 'fas fa-robot text-success',
        'User': 'fas fa-user text-info', 
        'Result': 'fas fa-check-circle text-warning',
        'Stream': 'fas fa-stream text-secondary'
    };
    
    const colorClass = logTypeColors[log.log_type] || 'bg-light border-start border-secondary border-3';
    const iconClass = logTypeIcons[log.log_type] || 'fas fa-circle text-secondary';
    
    // 处理内容中的换行符
    const content = log.content.replace(/↵/g, '\n');
    const lines = content.split('\n');
    const isMultiLine = lines.length > 3;
    
    let displayContent = '';
    if (isMultiLine) {
        const previewLines = lines.slice(0, 3).join('\n');
        const remainingCount = lines.length - 3;
        displayContent = `
            <div class="log-content-preview" id="preview-${index}">
                <pre class="mb-0">${escapeHtml(previewLines)}</pre>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleLogContent(${index})">
                        <i class="fas fa-chevron-down"></i> ...还有${remainingCount}行
                    </button>
                </div>
            </div>
            <div class="log-content-full d-none" id="full-${index}">
                <pre class="mb-0">${escapeHtml(content)}</pre>
                <div class="mt-2">
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleLogContent(${index})">
                        <i class="fas fa-chevron-up"></i> 收起
                    </button>
                </div>
            </div>
        `;
    } else {
        displayContent = `<pre class="mb-0">${escapeHtml(content)}</pre>`;
    }
    
    return `
        <div class="log-entry mb-3 p-3 rounded ${colorClass}">
            <div class="d-flex align-items-center mb-2">
                <i class="${iconClass} me-2"></i>
                <strong class="me-3">${log.log_type}</strong>
                <span class="badge bg-secondary me-2">${log.progress}</span>
                <small class="text-muted">${log.timestamp}</small>
            </div>
            <div class="log-content">
                ${displayContent}
            </div>
        </div>
    `;
}

function toggleLogContent(index) {
    const preview = $(`#preview-${index}`);
    const full = $(`#full-${index}`);
    
    if (preview.hasClass('d-none')) {
        preview.removeClass('d-none');
        full.addClass('d-none');
    } else {
        preview.addClass('d-none');
        full.removeClass('d-none');
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function updateStats(logs) {
    const totalCount = $('#logsContainer .log-entry').length;
    $('#totalLogsCount').text(totalCount);
    $('#lastUpdateTime').text(new Date().toLocaleTimeString());
}

function refreshLogs() {
    lastTimestamp = null;
    loadLogs(false);
}

function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        isAutoRefreshEnabled = false;
        $('#autoRefreshIcon').removeClass('fa-stop').addClass('fa-play');
        $('#autoRefreshText').text('开启自动刷新');
        $('#autoRefreshStatus').text('关闭');
    } else {
        // 开启自动刷新
        autoRefreshInterval = setInterval(() => {
            loadLogs(true); // 增量加载
        }, 3000); // 每3秒刷新一次
        isAutoRefreshEnabled = true;
        $('#autoRefreshIcon').removeClass('fa-play').addClass('fa-stop');
        $('#autoRefreshText').text('停止自动刷新');
        $('#autoRefreshStatus').text('开启');
    }
}

function clearLogs() {
    if (confirm('确定要清空当前显示的日志吗？这不会删除服务器上的日志文件。')) {
        $('#logsContainer').html(`
            <div class="text-center text-muted py-5">
                <i class="fas fa-file-alt fa-2x mb-3"></i>
                <div>日志已清空，点击刷新重新加载</div>
            </div>
        `);
        $('#totalLogsCount').text('0');
        lastTimestamp = null;
    }
}

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }
});
</script>
{% endblock %}
