{% extends "base.html" %}

{% block title %}{{ project.name }} - 任务列表 - AI任务管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <a href="{{ url_for('projects') }}" class="text-decoration-none text-muted">
            <i class="fas fa-arrow-left"></i>
        </a>
        {{ project.name }} - 任务列表
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
                <button type="button" class="btn btn-success" onclick="runTasks()">
                    <i class="fas fa-play"></i> 运行任务
                </button>
                <button type="button" class="btn btn-warning" onclick="resetTasks()">
                    <i class="fas fa-undo"></i> 重置任务
                </button>
                <button type="button" class="btn btn-info" id="stopExecutionBtn" onclick="stopExecution()" >
                    <i class="fas fa-stop"></i> 停止运行
                </button>
                <button type="button" class="btn btn-danger" onclick="regenerateTasks('{{ project.project_id }}')">
                    <i class="fas fa-redo"></i> 生成任务
                </button>
            <a href="/project/{{ project.project_id }}/files" class="btn btn-outline-info">
                <i class="fas fa-folder-open"></i> 文件预览
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshTasks()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button type="button" class="btn btn-outline-primary" onclick="showAddTaskModal()">
                <i class="fas fa-plus"></i> 添加任务
            </button>
        </div>
    </div>
</div>

<!-- 项目信息卡片 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-info-circle"></i> 项目信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>项目名称:</strong> {{ project.name }}</p>
                        <p>
                            <strong>LLM Provider:</strong> 
                            {% if project.provider == "local" %}
                                <span class="badge bg-secondary">内网</span>
                            {% elif project.provider == "zhipu" %}
                                <span class="badge bg-info text-dark">智谱</span>
                            {% elif project.provider == "claude" %}
                                <span class="badge bg-primary">Claude</span>
                            {% else %}
                                <span class="badge bg-light text-dark">{{ project.provider }}</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建时间:</strong> {{ project.created_at[:19].replace('T', ' ') }}</p>
                        <p>
                            <strong>Agent状态:</strong>
                            {% if project.run_state %}
                                <span class="badge bg-success">运行中</span>
                            {% else %}
                                <span class="badge bg-secondary">未运行</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
                {% if project.requirement %}
                <div class="mt-3">
                    <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                    <div class="border rounded p-3 bg-light position-relative" style="max-height: 150px; overflow: hidden;" id="requirement-container">
                        <div id="requirement-content">{{ project.requirement }}</div>
                        <div id="requirement-overlay" class="position-absolute bottom-0 start-0 end-0 bg-gradient-to-t from-light to-transparent h-50 d-flex justify-content-center align-items-end pb-2" style="display: none;">
                            <button id="toggle-requirement" class="btn btn-sm btn-outline-primary" type="button">查看更多</button>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-chart-bar"></i> 任务统计</h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="h4 mb-0 text-info">{{ tasks|length }}</div>
                        <small class="text-muted">总任务</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-success">{{ tasks|selectattr('status', 'equalto', 'completed')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">已完成</small>
                    </div>
                </div>
                <div class="row text-center mt-3">
                    <div class="col-6">
                        <div class="h4 mb-0 text-warning">{{ tasks|selectattr('status', 'equalto', 'in_progress')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">进行中</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-danger">{{ tasks|selectattr('status', 'equalto', 'failed')|list|length if tasks else 0 }}</div>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
{% if tasks %}
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-tasks"></i> 任务列表</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 5%">#</th>
                            <th style="width: 25%">任务标题</th>
                            <th style="width: 30%">任务描述</th>
                            <th style="width: 10%">状态</th>
                            <th style="width: 10%">优先级</th>
                            <th style="width: 10%">执行时间</th>
                            <th style="width: 10%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in tasks %}
                        <tr>
                            <td>{{ loop.index }}</td>
                            <td>
                                <div class="fw-bold">{{ task.get('title', task.get('name', 'N/A')) }}</div>
                                {% if task.get('dependencies') %}
                                    <small class="text-muted">
                                        <i class="fas fa-link"></i> 依赖: {{ task.dependencies|join(', ') }}
                                    </small>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ (task.get('description', '')[:100]) }}{% if task.get('description', '')|length > 100 %}...{% endif %}</small>
                            </td>
                            <td>
                                {% set status = task.get('status', 'unknown') %}
                                {% if status == 'pending' %}
                                    <span class="badge bg-secondary">待处理</span>
                                {% elif status == 'in_progress' %}
                                    <span class="badge bg-primary">进行中</span>
                                {% elif status == 'completed' %}
                                    <span class="badge bg-success">已完成</span>
                                {% elif status == 'failed' %}
                                    <span class="badge bg-danger">失败</span>
                                {% elif status == 'cancelled' %}
                                    <span class="badge bg-warning">已取消</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% set priority = task.get('priority', 'medium') %}
                                {% if priority == 'high' %}
                                    <span class="badge bg-danger">高</span>
                                {% elif priority == 'medium' %}
                                    <span class="badge bg-warning">中</span>
                                {% elif priority == 'low' %}
                                    <span class="badge bg-secondary">低</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ priority }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if task.get('execution_time') %}
                                    {% set exec_time = task.execution_time %}
                                    {% if exec_time < 60 %}
                                        {{ "%.2f"|format(exec_time) }}秒
                                    {% else %}
                                        {{ "%.1f"|format(exec_time / 60) }}分钟
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    {% if task.get('status', 'pending') == 'pending' %}
                                        <button type="button" class="btn btn-outline-primary edit-task-btn"
                                                data-task-id="{{ task.get('id', '') }}" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger delete-task-btn"
                                                data-task-id="{{ task.get('id', '') }}" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-info view-detail-btn"
                                            data-task-id="{{ task.get('id', '') }}" title="详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success run-task-btn"
                                            data-task-id="{{ task.get('id', '') }}" title="运行">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning view-llm-logs-btn"
                                            data-task-id="{{ task.get('id', '') }}" title="LLM日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-tasks fa-5x text-muted mb-4"></i>
        <h3 class="text-muted">还没有任务</h3>
        {% if not project.tasks_generated %}
            <p class="text-muted mb-4">为项目生成任务来开始工作</p>
            <button class="btn btn-primary btn-lg" onclick="generateTasks('{{ project.project_id }}')">
                <i class="fas fa-cogs"></i> 生成任务
            </button>
        {% else %}
            <p class="text-muted mb-4">任务加载失败或任务文件不存在</p>
            <button class="btn btn-warning btn-lg" onclick="regenerateTasks('{{ project.project_id }}')">
                <i class="fas fa-redo"></i> 重新生成任务
            </button>
        {% endif %}
    </div>
{% endif %}
{% endblock %}

{% block modals %}
<!-- 添加任务模态框 -->
<div class="modal fade" id="addTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加新任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTaskForm">
                    <div class="mb-3">
                        <label for="taskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="taskTitle" required
                               placeholder="请输入任务标题">
                    </div>
                    <div class="mb-3">
                        <label for="taskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="taskDescription" rows="4"
                                  placeholder="请输入任务描述"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="taskPriority" class="form-label">优先级</label>
                        <select class="form-control" id="taskPriority">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="taskDependencies" class="form-label">依赖任务 (可选)</label>
                        <input type="text" class="form-control" id="taskDependencies"
                               placeholder="输入依赖的任务ID，多个用逗号分隔">
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            如果此任务依赖其他任务，请输入依赖任务的ID
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="executeImmediately">
                            <label class="form-check-label" for="executeImmediately">
                                立即执行此任务
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitAddTask()">添加任务</button>
            </div>
        </div>
    </div>
</div>

<!-- 生成任务确认模态框 -->
<div class="modal fade" id="generateTasksModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="generateTasksContent">
                    <!-- 内容将通过JavaScript加载 -->
                </div>
                <div class="mt-3" id="taskCountSection" style="display: none;">
                    <label for="taskCount" class="form-label">任务数量 (可选)</label>
                    <input type="number" class="form-control" id="taskCount" min="1" max="20"
                           placeholder="留空则自动确定任务数量">
                    <div class="form-text">
                        <i class="fas fa-info-circle"></i>
                        建议生成5-10个任务，留空将根据需求自动确定
                    </div>
                </div>
                <input type="hidden" id="generateTasksProjectId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGenerateTasks()">确认生成</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 渲染Markdown内容
    $(document).ready(function() {
        const requirementContent = $('#requirement-content');
        if (requirementContent.length && requirementContent.text().trim()) {
            requirementContent.html(marked.parse(requirementContent.text()));
        }
        
        // 绑定任务操作按钮事件
        bindTaskActionEvents();
        
        // 检查需求内容是否超出高度限制
        checkRequirementHeight();
        
        // 检查项目运行状态
        checkProjectRunState();
    });
    
    function checkProjectRunState() {
        // 如果项目正在运行，显示停止按钮
        // 使用纯JavaScript方式检查，避免模板语法与JavaScript语法冲突
        var isRunning = $('#stopExecutionBtn').data('running') === true;
    }
    
    function checkRequirementHeight() {
        const container = $('#requirement-container');
        const content = $('#requirement-content');
        const overlay = $('#requirement-overlay');
        const toggleButton = $('#toggle-requirement');
        
        if (container.length && content.length) {
            // 检查内容是否超出容器高度
            if (content.outerHeight() > container.outerHeight()) {
                overlay.show();
                
                // 绑定展开/收起事件
                toggleButton.on('click', function() {
                    if (container.hasClass('expanded')) {
                        // 收起
                        container.removeClass('expanded');
                        container.css('max-height', '150px');
                        toggleButton.text('查看更多');
                    } else {
                        // 展开
                        container.addClass('expanded');
                        container.css('max-height', 'none');
                        toggleButton.text('收起');
                    }
                });
            }
        }
    }
    
    function bindTaskActionEvents() {
        // 编辑任务按钮
        $(document).on('click', '.edit-task-btn', function() {
            const taskId = $(this).data('task-id');
            editTask(taskId);
        });
        
        // 删除任务按钮
        $(document).on('click', '.delete-task-btn', function() {
            const taskId = $(this).data('task-id');
            deleteTask(taskId);
        });
        
        // 查看详情按钮
        $(document).on('click', '.view-detail-btn', function() {
            const taskId = $(this).data('task-id');
            viewTaskDetail(taskId);
        });
        
        // 运行任务按钮
        $(document).on('click', '.run-task-btn', function() {
            const taskId = $(this).data('task-id');
            runSingleTask(taskId);
        });
        
        // 查看LLM日志按钮
        $(document).on('click', '.view-llm-logs-btn', function() {
            const taskId = $(this).data('task-id');
            viewTaskLLMLogs(taskId);
        });
    }
    
    function refreshTasks() {
        location.reload();
    }

    function showAddTaskModal() {
        // 清空表单
        $('#addTaskForm')[0].reset();
        $('#addTaskModal').modal('show');
    }

    function submitAddTask() {
        const title = $('#taskTitle').val().trim();
        const description = $('#taskDescription').val().trim();
        const priority = $('#taskPriority').val();
        const dependencies = $('#taskDependencies').val().trim();
        const executeImmediately = $('#executeImmediately').is(':checked');

        if (!title) {
            showAlert('请输入任务标题', 'warning');
            return;
        }

        // 处理依赖任务ID
        const dependencyList = dependencies ? dependencies.split(',').map(id => id.trim()).filter(id => id) : [];

        // 显示加载状态
        $('#addTaskModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 添加中...');

        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                title: title,
                description: description,
                priority: priority,
                dependencies: dependencyList,
                execute_immediately: executeImmediately
            }),
            success: function(response) {
                if (response.success) {
                    let message = '任务添加成功！';
                    if (executeImmediately && response.execution_result) {
                        if (response.execution_result.success) {
                            message += ' 任务已开始执行。';
                        } else {
                            message += ` 但执行失败: ${response.execution_result.message}`;
                        }
                    }
                    showAlert(message, 'success');
                    $('#addTaskModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('任务添加失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务添加失败: ' + (response.error || '未知错误'), 'danger');
            },
            complete: function() {
                $('#addTaskModal .btn-primary').prop('disabled', false).html('添加任务');
            }
        });
    }
    
    function generateTasks(projectId) {
        // 获取项目信息并显示生成任务确认
        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'GET',
            success: function(project) {
                let content = `<p>确定要为项目 <strong>${project.name}</strong> 生成任务吗？</p>`;

                if (!project.requirement || !project.requirement.trim()) {
                    content = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            项目 <strong>${project.name}</strong> 还没有设置需求，无法生成任务。
                        </div>
                        <p>请先编辑项目并添加需求内容。</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').hide();
                    $('#taskCountSection').hide();
                } else if (project.tasks_generated) {
                    content = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            项目 <strong>${project.name}</strong> 已经生成过任务。
                        </div>
                        <p>重新生成将覆盖现有任务，确定要继续吗？</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                } else {
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                }

                $('#generateTasksContent').html(content);
                $('#generateTasksProjectId').val(projectId);
                $('#generateTasksModal').modal('show');
            },
            error: function() {
                showAlert('获取项目信息失败', 'danger');
            }
        });
    }
    
    function regenerateTasks(projectId) {
        generateTasks(projectId);
    }
    
    function confirmGenerateTasks() {
        const projectId = $('#generateTasksProjectId').val();
        const taskCount = $('#taskCount').val();

        // 显示加载状态
        $('#generateTasksModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 生成中...');

        const requestData = {};
        if (taskCount && taskCount.trim()) {
            requestData.num_tasks = parseInt(taskCount);
        }

        $.ajax({
            url: `/api/projects/${projectId}/generate_tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                if (response.success) {
                    showAlert('任务生成成功！', 'success');
                    $('#generateTasksModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('任务生成失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                $('#generateTasksModal .btn-primary').prop('disabled', false).html('确认生成');
            }
        });
    }
    
    function runTasks() {
        if (confirm('确定要运行所有任务吗？')) {
            // 调用运行任务API
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/run_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务开始运行！', 'success');
                        // 显示停止按钮
                        $('#stopExecutionBtn').show();
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
    
    function stopExecution() {
        if (confirm('确定要停止所有任务的执行吗？')) {
            // 调用停止执行API
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/stop_execution`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('已发送停止执行命令！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('停止执行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('停止执行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
    
    function resetTasks() {
        if (confirm('确定要重置所有任务的状态吗？这将清除所有执行结果。')) {
            // 调用重置任务API
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/reset`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('所有任务已重置！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务重置失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务重置失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }
    
    function editTask(taskId) {
        // 获取任务信息
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'GET',
            success: function(response) {
                const task = response.tasks.find(t => t.id == taskId);
                if (task) {
                    // 显示编辑模态框
                    showEditTaskModal(task);
                } else {
                    showAlert('任务不存在', 'danger');
                }
            },
            error: function() {
                showAlert('获取任务信息失败', 'danger');
            }
        });
    }

    function showEditTaskModal(task) {
        const editModalHtml = `
            <div class="modal fade" id="editTaskModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑任务</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="editTaskForm">
                                <div class="mb-3">
                                    <label for="editTaskTitle" class="form-label">任务标题 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="editTaskTitle" value="${task.title || task.name || ''}" required>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDescription" class="form-label">任务描述</label>
                                    <textarea class="form-control" id="editTaskDescription" rows="4">${task.description || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDetails" class="form-label">详细信息</label>
                                    <textarea class="form-control" id="editTaskDetails" rows="3" placeholder="任务的详细实现步骤和要求">${task.details || ''}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskTestStrategy" class="form-label">测试策略</label>
                                    <input type="text" class="form-control" id="editTaskTestStrategy" placeholder="如何验证任务完成" value="${task.testStrategy || ''}">
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskPriority" class="form-label">优先级</label>
                                    <select class="form-control" id="editTaskPriority">
                                        <option value="low" ${task.priority === 'low' ? 'selected' : ''}>低</option>
                                        <option value="medium" ${task.priority === 'medium' ? 'selected' : ''}>中</option>
                                        <option value="high" ${task.priority === 'high' ? 'selected' : ''}>高</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="editTaskDependencies" class="form-label">依赖任务 (可选)</label>
                                    <input type="text" class="form-control" id="editTaskDependencies"
                                           value="${task.dependencies ? task.dependencies.join(', ') : ''}"
                                           placeholder="输入依赖的任务ID，多个用逗号分隔">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="submitEditTask('${task.id}')">保存修改</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        $('#editTaskModal').remove();

        // 添加新的模态框并显示
        $('body').append(editModalHtml);
        $('#editTaskModal').modal('show');

        // 模态框关闭后移除
        $('#editTaskModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    function submitEditTask(taskId) {
        const title = $('#editTaskTitle').val().trim();
        const description = $('#editTaskDescription').val().trim();
        const details = $('#editTaskDetails').val().trim();
        const testStrategy = $('#editTaskTestStrategy').val().trim();
        const priority = $('#editTaskPriority').val();
        const dependencies = $('#editTaskDependencies').val().trim();

        if (!title) {
            showAlert('请输入任务标题', 'warning');
            return;
        }

        // 处理依赖任务ID
        const dependencyList = dependencies ? dependencies.split(',').map(id => id.trim()).filter(id => id) : [];

        // 显示加载状态
        $('#editTaskModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks/${taskId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                title: title,
                description: description,
                details: details,
                testStrategy: testStrategy,
                priority: priority,
                dependencies: dependencyList
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('任务更新成功！', 'success');
                    $('#editTaskModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('任务更新失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务更新失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                $('#editTaskModal .btn-primary').prop('disabled', false).html('保存修改');
            }
        });
    }

    function deleteTask(taskId) {
        if (confirm('确定要删除这个任务吗？此操作不可撤销。')) {
            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/${taskId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务删除成功！', 'success');
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert('任务删除失败: ' + response.error, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务删除失败: ' + (response.error || '未知错误'), 'danger');
                }
            });
        }
    }

    function runSingleTask(taskId) {
        if (confirm('确定要运行这个任务吗？')) {
            // 显示加载状态
            const button = $(`button[onclick="runSingleTask('${taskId}')"]`);
            const originalHtml = button.html();
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');

            $.ajax({
                url: `/api/projects/{{ project.project_id }}/tasks/${taskId}/run`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务运行成功！', 'success');
                        if (response.logs && response.logs.length > 0) {
                            // 显示运行日志
                            const logHtml = response.logs.join('<br>');
                            showAlert(`任务运行完成！<br><small>${logHtml}</small>`, 'success');
                        }
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.error || '未知错误'), 'danger');
                },
                complete: function() {
                    button.prop('disabled', false).html(originalHtml);
                }
            });
        }
    }

    function viewTaskLLMLogs(taskId) {
        // 跳转到LLM日志页面
        window.location.href = `/project/{{ project.project_id }}/tasks/${taskId}/llm-logs`;
    }

    function viewTaskDetail(taskId) {
        // 获取任务详情
        $.ajax({
            url: `/api/projects/{{ project.project_id }}/tasks`,
            method: 'GET',
            success: function(response) {
                const task = response.tasks.find(t => t.id == taskId);
                if (task) {
                    const detailHtml = `
                        <div class="modal fade" id="taskDetailModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">任务详情</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <table class="table">
                                            <tr><td><strong>任务ID:</strong></td><td>${task.id}</td></tr>
                                            <tr><td><strong>标题:</strong></td><td>${task.title || task.name || 'N/A'}</td></tr>
                                            <tr><td><strong>描述:</strong></td><td>${task.description || '无'}</td></tr>
                                            <tr><td><strong>状态:</strong></td><td>${task.status || '未知'}</td></tr>
                                            <tr><td><strong>优先级:</strong></td><td>${task.priority || '中'}</td></tr>
                                            <tr><td><strong>依赖:</strong></td><td>${task.dependencies ? task.dependencies.join(', ') : '无'}</td></tr>
                                            <tr><td><strong>创建时间:</strong></td><td>${task.created_at || '未知'}</td></tr>
                                            <tr><td><strong>更新时间:</strong></td><td>${task.updated_at || '未知'}</td></tr>
                                        </table>
                                        ${task.result ? '<h6>执行结果:</h6><pre>' + task.result + '</pre>' : ''}
                                        ${task.error_message ? '<h6>错误信息:</h6><pre class="text-danger">' + task.error_message + '</pre>' : ''}
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // 移除已存在的模态框
                    $('#taskDetailModal').remove();

                    // 添加新的模态框并显示
                    $('body').append(detailHtml);
                    $('#taskDetailModal').modal('show');

                    // 模态框关闭后移除
                    $('#taskDetailModal').on('hidden.bs.modal', function() {
                        $(this).remove();
                    });
                } else {
                    showAlert('任务不存在', 'danger');
                }
            },
            error: function() {
                showAlert('获取任务信息失败', 'danger');
            }
        });
    }
</script>
{% endblock %}
