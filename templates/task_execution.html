{% extends "base.html" %}

{% block title %}任务执行管理 - {{ requirement.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('project_detail', project_id=project.project_id) }}">{{ project.name }}</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('requirement_detail', req_id=requirement.req_id) }}">{{ requirement.title }}</a></li>
                <li class="breadcrumb-item active">任务执行</li>
            </ol>
        </nav>
        <h1 class="h2">任务执行管理</h1>
        <p class="text-muted">{{ requirement.title }} - 任务执行控制和监控</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="quickTask()">
                <i class="fas fa-bolt"></i> 快速任务
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 执行控制面板 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">执行控制</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-lg" onclick="startExecution('parallel')" id="startParallelBtn">
                                <i class="fas fa-play"></i> 并行执行
                            </button>
                            <button class="btn btn-info btn-lg" onclick="startExecution('sequential')" id="startSequentialBtn">
                                <i class="fas fa-step-forward"></i> 顺序执行
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning btn-lg" onclick="stopExecution()" id="stopBtn" disabled>
                                <i class="fas fa-stop"></i> 停止执行
                            </button>
                            <button class="btn btn-secondary btn-lg" onclick="resetAllTasks()" id="resetBtn">
                                <i class="fas fa-undo"></i> 重置所有任务
                            </button>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="autoRestart" checked>
                            <label class="form-check-label" for="autoRestart">
                                失败时自动重试
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="continueOnError">
                            <label class="form-check-label" for="continueOnError">
                                出错时继续执行
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2">
                            <label for="maxRetries" class="form-label">最大重试次数</label>
                            <input type="number" class="form-control" id="maxRetries" value="3" min="0" max="10">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">执行状态</h5>
            </div>
            <div class="card-body text-center" id="execution-status">
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h5>等待执行</h5>
                <p class="text-muted">点击执行按钮开始任务</p>
            </div>
        </div>
    </div>
</div>

<!-- 任务进度 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">任务进度</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="refreshProgress()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="d-flex justify-content-between">
                <span>总体进度</span>
                <span id="overall-progress-text">0%</span>
            </div>
            <div class="progress mb-2">
                <div class="progress-bar" id="overall-progress-bar" style="width: 0%"></div>
            </div>
        </div>
        
        {% if tasks %}
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>任务</th>
                            <th>状态</th>
                            <th>进度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="task-progress-table">
                        {% for task in tasks %}
                        <tr data-task-id="{{ task.id }}">
                            <td>
                                <strong>{{ task.title }}</strong>
                                <br><small class="text-muted">ID: {{ task.id }}</small>
                            </td>
                            <td>
                                <span class="task-status">{{ getStatusBadge(task.status)|safe }}</span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar task-progress" 
                                         data-task-id="{{ task.id }}"
                                         style="width: {% if task.status == 'completed' %}100{% elif task.status == 'running' %}50{% else %}0{% endif %}%">
                                        {% if task.status == 'completed' %}100%{% elif task.status == 'running' %}50%{% else %}0%{% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewTaskLog({{ task.id }})" title="查看日志">
                                        <i class="fas fa-file-alt"></i>
                                    </button>
                                    {% if task.status in ['completed', 'failed'] %}
                                    <button class="btn btn-outline-warning" onclick="restartTask({{ task.id }})" title="重新开始">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                    {% endif %}
                                    {% if task.status in ['completed', 'failed', 'cancelled'] %}
                                    <button class="btn btn-outline-secondary" onclick="resetTask({{ task.id }})" title="重置状态">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <p class="text-muted">还没有任务，请先生成任务</p>
                <a href="{{ url_for('task_management', req_id=requirement.req_id) }}" class="btn btn-primary">
                    <i class="fas fa-cogs"></i> 任务管理
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 执行日志 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">执行日志</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-outline-info" onclick="exportLogs()">
                <i class="fas fa-download"></i> 导出
            </button>
            <button class="btn btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash"></i> 清空
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <select class="form-select form-select-sm" id="logLevelFilter" onchange="filterLogs()">
                        <option value="">所有级别</option>
                        <option value="DEBUG">调试</option>
                        <option value="INFO">信息</option>
                        <option value="WARNING">警告</option>
                        <option value="ERROR">错误</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select form-select-sm" id="logTypeFilter" onchange="filterLogs()">
                        <option value="">所有类型</option>
                        <option value="task_start">任务开始</option>
                        <option value="task_complete">任务完成</option>
                        <option value="task_error">任务错误</option>
                        <option value="execution_start">执行开始</option>
                        <option value="execution_complete">执行完成</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <input type="text" class="form-control form-control-sm" id="logSearchInput" 
                           placeholder="搜索日志..." onkeyup="searchLogs()">
                </div>
                <div class="col-md-3">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="autoScrollLogs" checked>
                        <label class="form-check-label" for="autoScrollLogs">
                            自动滚动
                        </label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="log-container" id="log-container" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
            {% if logs %}
                {% for log in logs %}
                <div class="log-entry log-level-{{ log.level.lower() }}" data-level="{{ log.level }}" data-type="{{ log.event_type }}">
                    <small class="text-muted">{{ log.timestamp[:19] }}</small>
                    <span class="badge bg-secondary">{{ log.event_type }}</span>
                    {% if log.task_id %}
                        <span class="badge bg-info">Task-{{ log.task_id }}</span>
                    {% endif %}
                    <span class="log-message">{{ log.message }}</span>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-muted text-center">暂无执行日志</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 快速任务模态框 -->
<div class="modal fade" id="quickTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">快速任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">一句话描述需求，系统自动生成任务并启动运行</p>
                <form id="quickTaskForm">
                    <div class="mb-3">
                        <label for="quickTaskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="quickTaskDescription" rows="3"
                                  placeholder="例如：创建一个用户登录功能，包括前端界面和后端API" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quickTaskPriority" class="form-label">优先级</label>
                                <select class="form-select" id="quickTaskPriority">
                                    <option value="high">高</option>
                                    <option value="medium" selected>中</option>
                                    <option value="low">低</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quickTaskComplexity" class="form-label">复杂度</label>
                                <select class="form-select" id="quickTaskComplexity">
                                    <option value="simple">简单</option>
                                    <option value="medium" selected>中等</option>
                                    <option value="complex">复杂</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="quickTaskAutoRun" checked>
                        <label class="form-check-label" for="quickTaskAutoRun">
                            生成后自动运行
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeQuickTask()">
                    <i class="fas fa-bolt"></i> 执行
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 任务日志模态框 -->
<div class="modal fade" id="taskLogModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务日志</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="log-container" id="task-log-content" style="height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 5px;">
                    <!-- 任务日志内容 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 重新开始任务模态框 -->
<div class="modal fade" id="restartTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">重新开始任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>重新开始任务将使用新的会话ID，并总结之前的结果。</p>
                <div class="mb-3">
                    <label for="restartReason" class="form-label">重启原因（可选）</label>
                    <textarea class="form-control" id="restartReason" rows="2"
                              placeholder="说明为什么要重新开始这个任务..."></textarea>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="keepPreviousResult" checked>
                    <label class="form-check-label" for="keepPreviousResult">
                        保留之前的执行结果作为参考
                    </label>
                </div>
                <input type="hidden" id="restartTaskId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="confirmRestartTask()">
                    <i class="fas fa-redo"></i> 重新开始
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let executionStatusInterval;
    let progressUpdateInterval;

    $(document).ready(function() {
        // 检查执行状态
        checkExecutionStatus();

        // 定期更新状态和进度
        executionStatusInterval = setInterval(checkExecutionStatus, 3000);
        progressUpdateInterval = setInterval(updateProgress, 5000);
    });

    function checkExecutionStatus() {
        $.ajax({
            url: `/api/requirements/{{ requirement.req_id }}/task_status`,
            method: 'GET',
            success: function(status) {
                updateExecutionStatus(status);
            },
            error: function() {
                // 忽略错误，继续检查
            }
        });
    }

    function updateExecutionStatus(status) {
        const statusDiv = $('#execution-status');
        const startParallelBtn = $('#startParallelBtn');
        const startSequentialBtn = $('#startSequentialBtn');
        const stopBtn = $('#stopBtn');
        const resetBtn = $('#resetBtn');

        if (status.status === 'running') {
            statusDiv.html(`
                <div class="spinner-border text-primary mb-3" role="status"></div>
                <h5>执行中...</h5>
                <p class="text-muted">开始时间: ${formatDateTime(status.start_time)}</p>
            `);
            startParallelBtn.prop('disabled', true);
            startSequentialBtn.prop('disabled', true);
            stopBtn.prop('disabled', false);
            resetBtn.prop('disabled', true);
        } else if (status.status === 'completed') {
            statusDiv.html(`
                <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                <h5>执行完成</h5>
                <p class="text-muted">完成时间: ${formatDateTime(status.end_time)}</p>
            `);
            startParallelBtn.prop('disabled', false);
            startSequentialBtn.prop('disabled', false);
            stopBtn.prop('disabled', true);
            resetBtn.prop('disabled', false);
        } else if (status.status === 'failed') {
            statusDiv.html(`
                <i class="fas fa-exclamation-circle text-danger fa-3x mb-3"></i>
                <h5>执行失败</h5>
                <p class="text-muted">结束时间: ${formatDateTime(status.end_time)}</p>
            `);
            startParallelBtn.prop('disabled', false);
            startSequentialBtn.prop('disabled', false);
            stopBtn.prop('disabled', true);
            resetBtn.prop('disabled', false);
        } else if (status.status === 'stopped') {
            statusDiv.html(`
                <i class="fas fa-stop-circle text-warning fa-3x mb-3"></i>
                <h5>已停止</h5>
                <p class="text-muted">停止时间: ${formatDateTime(status.end_time)}</p>
            `);
            startParallelBtn.prop('disabled', false);
            startSequentialBtn.prop('disabled', false);
            stopBtn.prop('disabled', true);
            resetBtn.prop('disabled', false);
        } else {
            statusDiv.html(`
                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                <h5>等待执行</h5>
                <p class="text-muted">点击执行按钮开始任务</p>
            `);
            startParallelBtn.prop('disabled', false);
            startSequentialBtn.prop('disabled', false);
            stopBtn.prop('disabled', true);
            resetBtn.prop('disabled', false);
        }
    }

    function startExecution(mode) {
        const autoRestart = $('#autoRestart').prop('checked');
        const continueOnError = $('#continueOnError').prop('checked');
        const maxRetries = parseInt($('#maxRetries').val()) || 3;

        if (confirm(`确定要开始${mode === 'parallel' ? '并行' : '顺序'}执行任务吗？`)) {
            showAlert('任务开始执行，请稍候...', 'info');

            $.ajax({
                url: `/api/requirements/{{ requirement.req_id }}/run_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    parallel_mode: mode === 'parallel',
                    auto_restart: autoRestart,
                    continue_on_error: continueOnError,
                    max_retries: maxRetries
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已开始执行！', 'success');
                    } else {
                        showAlert('执行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('执行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function stopExecution() {
        if (confirm('确定要停止任务执行吗？')) {
            $.ajax({
                url: `/api/requirements/{{ requirement.req_id }}/stop_tasks`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已停止！', 'warning');
                    } else {
                        showAlert('停止失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('停止失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function resetAllTasks() {
        if (confirm('确定要重置所有任务的状态吗？这将清除所有执行结果。')) {
            showAlert('重置所有任务功能正在开发中...', 'warning');
        }
    }

    function updateProgress() {
        // 更新任务进度
        const tasks = {{ tasks|tojson|safe }};
        let completedTasks = 0;
        let totalTasks = tasks.length;

        tasks.forEach(task => {
            if (task.status === 'completed') {
                completedTasks++;
            }
        });

        const overallProgress = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;
        $('#overall-progress-bar').css('width', overallProgress + '%');
        $('#overall-progress-text').text(overallProgress + '%');

        // 更新进度条颜色
        const progressBar = $('#overall-progress-bar');
        progressBar.removeClass('bg-danger bg-warning bg-success');
        if (overallProgress < 30) {
            progressBar.addClass('bg-danger');
        } else if (overallProgress < 70) {
            progressBar.addClass('bg-warning');
        } else {
            progressBar.addClass('bg-success');
        }
    }

    function refreshProgress() {
        location.reload();
    }

    function viewTaskLog(taskId) {
        // 获取特定任务的日志
        $.ajax({
            url: `/api/requirements/{{ requirement.req_id }}/logs?task_id=${taskId}`,
            method: 'GET',
            success: function(logs) {
                let logContent = '';
                if (logs && logs.length > 0) {
                    logs.forEach(log => {
                        logContent += `
                            <div class="log-entry log-level-${log.level.toLowerCase()}">
                                <small class="text-muted">${log.timestamp.substring(0, 19)}</small>
                                <span class="badge bg-secondary">${log.event_type}</span>
                                <span class="log-message">${log.message}</span>
                            </div>
                        `;
                    });
                } else {
                    logContent = '<p class="text-muted text-center">暂无日志</p>';
                }

                $('#task-log-content').html(logContent);
                $('#taskLogModal').modal('show');
            },
            error: function() {
                showAlert('获取任务日志失败', 'danger');
            }
        });
    }

    function restartTask(taskId) {
        $('#restartTaskId').val(taskId);
        $('#restartTaskModal').modal('show');
    }

    function confirmRestartTask() {
        const taskId = $('#restartTaskId').val();
        const reason = $('#restartReason').val().trim();
        const keepPrevious = $('#keepPreviousResult').prop('checked');

        showAlert('重新开始任务功能正在开发中...', 'warning');
        $('#restartTaskModal').modal('hide');
    }

    function resetTask(taskId) {
        if (confirm(`确定要重置任务 ${taskId} 的状态吗？这将清除执行结果。`)) {
            showAlert('重置任务状态功能正在开发中...', 'warning');
        }
    }

    function quickTask() {
        $('#quickTaskModal').modal('show');
    }

    function executeQuickTask() {
        const description = $('#quickTaskDescription').val().trim();
        const priority = $('#quickTaskPriority').val();
        const complexity = $('#quickTaskComplexity').val();
        const autoRun = $('#quickTaskAutoRun').prop('checked');

        if (!description) {
            showAlert('请输入任务描述', 'warning');
            return;
        }

        showAlert('正在生成快速任务，请稍候...', 'info');
        $('#quickTaskModal').modal('hide');

        // 这里应该调用快速任务API
        showAlert('快速任务功能正在开发中...', 'warning');
    }

    function refreshLogs() {
        location.reload();
    }

    function exportLogs() {
        // 导出日志功能
        showAlert('导出日志功能正在开发中...', 'warning');
    }

    function clearLogs() {
        if (confirm('确定要清空所有执行日志吗？此操作不可撤销。')) {
            showAlert('清空日志功能正在开发中...', 'warning');
        }
    }

    function filterLogs() {
        const levelFilter = $('#logLevelFilter').val();
        const typeFilter = $('#logTypeFilter').val();

        $('.log-entry').each(function() {
            const level = $(this).data('level');
            const type = $(this).data('type');

            let show = true;
            if (levelFilter && level !== levelFilter) {
                show = false;
            }
            if (typeFilter && type !== typeFilter) {
                show = false;
            }

            if (show) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    function searchLogs() {
        const searchTerm = $('#logSearchInput').val().toLowerCase();

        $('.log-entry').each(function() {
            const logText = $(this).text().toLowerCase();
            if (searchTerm === '' || logText.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // 自动滚动日志
    function autoScrollLogs() {
        if ($('#autoScrollLogs').prop('checked')) {
            const logContainer = $('#log-container');
            logContainer.scrollTop(logContainer[0].scrollHeight);
        }
    }

    // 定期自动滚动
    setInterval(autoScrollLogs, 2000);

    // 页面卸载时清除定时器
    $(window).on('beforeunload', function() {
        if (executionStatusInterval) {
            clearInterval(executionStatusInterval);
        }
        if (progressUpdateInterval) {
            clearInterval(progressUpdateInterval);
        }
    });
</script>
{% endblock %}
