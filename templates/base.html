<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}AI任务管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            min-height: 100vh;
        }
        .task-status-pending { color: #6c757d; }
        .task-status-in_progress { color: #0d6efd; }
        .task-status-completed { color: #198754; }
        .task-status-failed { color: #dc3545; }
        .task-status-cancelled { color: #fd7e14; }
        
        .priority-high { color: #dc3545; font-weight: bold; }
        .priority-medium { color: #fd7e14; }
        .priority-low { color: #6c757d; }
        
        .log-level-DEBUG { color: #6c757d; }
        .log-level-INFO { color: #0d6efd; }
        .log-level-WARNING { color: #fd7e14; }
        .log-level-ERROR { color: #dc3545; }
        .log-level-CRITICAL { color: #dc3545; font-weight: bold; }
        
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        
        .log-entry {
            margin-bottom: 0.5rem;
            padding: 0.25rem;
            border-left: 3px solid #dee2e6;
            padding-left: 0.75rem;
        }
        
        .task-card {
            transition: transform 0.2s;
        }
        
        .task-card:hover {
            transform: translateY(-2px);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-tasks"></i> AI任务管理系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('projects') }}">
                            <i class="fas fa-folder"></i> 项目管理
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('projects') }}">
                                <i class="fas fa-folder-open"></i> 项目列表
                            </a>
                        </li>
                    </ul>
                    
                    <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                        <span>快速操作</span>
                    </h6>
                    <ul class="nav flex-column mb-2">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="createProject()">
                                <i class="fas fa-plus"></i> 新建项目
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="pt-3">
                    <!-- Flash消息 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>


    <!-- 模态框 -->
    {% block modals %}{% endblock %}

    <!-- 创建项目模态框 -->
    <div class="modal fade" id="createProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建新项目</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createProjectForm">
                        <div class="mb-3">
                            <label for="createProjectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectName" required
                                   placeholder="请输入项目名称">
                        </div>
                        <div class="mb-3">
                            <label for="createProjectWorkDir" class="form-label">工作目录 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="createProjectWorkDir" required
                                   placeholder="例如: /tmp/projects/my_project">
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                项目文件将存储在此目录中
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectProvider" class="form-label">LLM Provider</label>
                            <select class="form-control" id="createProjectProvider">
                                <option value="local">内网</option>
                                <option value="zhipu">智谱</option>
                                <option value="claude">Claude</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectTaskType" class="form-label">任务类型</label>
                            <select class="form-control" id="createProjectTaskType">
                                <option value="新功能">新功能</option>
                                <option value="代码重构">代码重构</option>
                                <option value="PMO需求">PMO需求</option>
                                <option value="代码分析">代码分析</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectRequirement" class="form-label">项目需求 (支持Markdown)</label>
                            <textarea class="form-control" id="createProjectRequirement" rows="8"
                                      placeholder="请输入项目需求，支持Markdown格式...&#10;&#10;例如：&#10;# 项目需求&#10;&#10;## 功能要求&#10;1. 功能A&#10;2. 功能B&#10;&#10;## 技术要求&#10;- 使用Python&#10;- 支持REST API"></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                支持Markdown格式，如：# 标题、- 列表、**粗体**等
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="createProjectRulesConstraint" class="form-label">规则约束 (Markdown格式)</label>
                            <textarea class="form-control" id="createProjectRulesConstraint" rows="6"
                                      placeholder="支持Markdown语法，例如代码规范、架构约束等"></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle"></i>
                                规则约束将被写入到项目目录的CLAUDE.md文件中
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitCreateProject()">创建项目</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <script>
        // 通用函数
        function showAlert(message, type = 'info') {
            const alertDiv = $(`
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            $('.main-content .pt-3').prepend(alertDiv);
            
            // 5秒后自动消失
            setTimeout(() => {
                alertDiv.alert('close');
            }, 5000);
        }
        
        function formatDateTime(isoString) {
            if (!isoString) return '';
            const date = new Date(isoString);
            return date.toLocaleString('zh-CN');
        }
        
        function getStatusBadge(status) {
            const statusMap = {
                'pending': { class: 'secondary', text: '待处理' },
                'in_progress': { class: 'primary', text: '进行中' },
                'completed': { class: 'success', text: '已完成' },
                'failed': { class: 'danger', text: '失败' },
                'cancelled': { class: 'warning', text: '已取消' }
            };
            const info = statusMap[status] || { class: 'secondary', text: status };
            return `<span class="badge bg-${info.class}">${info.text}</span>`;
        }
        
        function getPriorityBadge(priority) {
            const priorityMap = {
                'high': { class: 'danger', text: '高' },
                'medium': { class: 'warning', text: '中' },
                'low': { class: 'secondary', text: '低' }
            };
            const info = priorityMap[priority] || { class: 'secondary', text: priority };
            return `<span class="badge bg-${info.class}">${info.text}</span>`;
        }
        
        function createProject() {
            // 显示创建项目模态框
            $('#createProjectModal').modal('show');

            // 自动填充工作目录
            $('#createProjectName').on('input', function() {
                const name = $(this).val().trim();
                if (name) {
                    const workDir = `/tmp/projects/${name.replace(/\s+/g, '_')}`;
                    $('#createProjectWorkDir').val(workDir);
                }
            });
        }

        function submitCreateProject() {
            const name = $('#createProjectName').val().trim();
            const workDir = $('#createProjectWorkDir').val().trim();
            const description = $('#createProjectDescription').val().trim();
            const requirement = $('#createProjectRequirement').val().trim();
            const provider = $('#createProjectProvider').val();
            const taskType = $('#createProjectTaskType').val();
            const rulesConstraint = $('#createProjectRulesConstraint').val().trim();

            if (!name) {
                showAlert('请输入项目名称', 'warning');
                return;
            }

            if (!workDir) {
                showAlert('请输入工作目录', 'warning');
                return;
            }

            // 显示加载状态
            $('#createProjectModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 创建中...');

            $.ajax({
                url: '/api/projects',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    name: name,
                    work_dir: workDir,
                    description: description,
                    requirement: requirement,
                    provider: provider,
                    task_type: taskType,
                    rules_constraint: rulesConstraint
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('项目创建成功！', 'success');
                        $('#createProjectModal').modal('hide');
                        setTimeout(() => {
                            window.location.href = '/projects';
                        }, 1000);
                    } else {
                        showAlert('项目创建失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('项目创建失败: ' + (response.message || '未知错误'), 'danger');
                },
                complete: function() {
                    $('#createProjectModal .btn-primary').prop('disabled', false).html('创建项目');
                }
            });
        }
        
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
