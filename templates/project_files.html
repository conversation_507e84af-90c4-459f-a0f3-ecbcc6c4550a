{% extends "base.html" %}

{% block title %}项目文件 - {{ project.name }}{% endblock %}

{% block extra_css %}
<style>
    .file-tree {
        max-height: 600px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 10px;
    }
    
    .file-tree ul {
        list-style: none;
        padding-left: 20px;
        margin: 0;
    }
    
    .file-tree li {
        margin: 2px 0;
    }
    
    .file-item {
        cursor: pointer;
        padding: 2px 5px;
        border-radius: 3px;
        display: flex;
        align-items: center;
    }
    
    .file-item:hover {
        background-color: #f8f9fa;
    }
    
    .file-item.selected {
        background-color: #007bff;
        color: white;
    }
    
    .file-item i {
        margin-right: 5px;
        width: 16px;
    }
    
    .file-content {
        height: 600px;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    
    .code-editor {
        height: 100%;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        border: none;
        resize: none;
        padding: 15px;
        background-color: #f8f9fa;
    }
    
    .file-info {
        background-color: #f8f9fa;
        padding: 10px;
        border-bottom: 1px solid #dee2e6;
        font-size: 12px;
        color: #6c757d;
    }
    
    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: #6c757d;
    }
    
    .markdown-content {
        padding: 20px;
        height: calc(100% - 40px);
        overflow-y: auto;
    }
    
    .json-content {
        padding: 15px;
        height: calc(100% - 30px);
        overflow-y: auto;
        background-color: #f8f9fa;
        font-family: 'Courier New', monospace;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">首页</a></li>
            <li class="breadcrumb-item"><a href="/projects">项目列表</a></li>
            <li class="breadcrumb-item"><a href="/project/{{ project.project_id }}/tasks">{{ project.name }}</a></li>
            <li class="breadcrumb-item active">文件预览</li>
        </ol>
    </nav>

    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-folder-open"></i> {{ project.name }} - 文件预览</h2>
        <div>
            <a href="/project/{{ project.project_id }}/tasks" class="btn btn-outline-primary">
                <i class="fas fa-tasks"></i> 返回任务列表
            </a>
        </div>
    </div>

    <div class="row">
        <!-- 左侧文件树 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-folder"></i> 文件目录
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="refreshFileTree()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="file-tree" id="fileTree">
                        <div class="loading">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧文件内容 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file"></i> 文件内容
                        <span id="fileName" class="text-muted"></span>
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="file-content" id="fileContent">
                        <div class="loading">
                            <i class="fas fa-info-circle"></i> 请选择左侧文件进行预览
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提示框 -->
<div id="alertContainer"></div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script>
    let currentProject = {{ project.to_dict() | tojson }};
    let selectedFile = null;

    $(document).ready(function() {
        loadFileTree();
    });

    function showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        $('#alertContainer').html(alertHtml);
        
        // 3秒后自动消失
        setTimeout(() => {
            $('.alert').alert('close');
        }, 3000);
    }

    function loadFileTree() {
        $.ajax({
            url: `/api/projects/${currentProject.project_id}/files`,
            method: 'GET',
            success: function(response) {
                if (response.files) {
                    renderFileTree(response.files);
                } else {
                    $('#fileTree').html('<div class="text-muted p-3">暂无文件</div>');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                $('#fileTree').html(`<div class="text-danger p-3">加载失败: ${response.error || '未知错误'}</div>`);
            }
        });
    }

    function renderFileTree(files) {
        const html = buildTreeHtml(files);
        $('#fileTree').html(`<ul>${html}</ul>`);
    }

    function buildTreeHtml(items) {
        let html = '';
        for (const item of items) {
            if (item.type === 'directory') {
                html += `
                    <li>
                        <div class="file-item" onclick="toggleDirectory(this)">
                            <i class="fas fa-folder"></i>
                            <span>${item.name}</span>
                        </div>
                        <ul style="display: none;">${buildTreeHtml(item.children || [])}</ul>
                    </li>
                `;
            } else {
                const icon = getFileIcon(item.name);
                html += `
                    <li>
                        <div class="file-item" onclick="selectFile('${item.path}', '${item.name}', this)">
                            <i class="${icon}"></i>
                            <span>${item.name}</span>
                            <small class="text-muted ms-auto">${formatFileSize(item.size)}</small>
                        </div>
                    </li>
                `;
            }
        }
        return html;
    }

    function getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            'js': 'fab fa-js-square text-warning',
            'py': 'fab fa-python text-primary',
            'java': 'fab fa-java text-danger',
            'html': 'fab fa-html5 text-danger',
            'css': 'fab fa-css3-alt text-primary',
            'json': 'fas fa-code text-success',
            'md': 'fab fa-markdown text-dark',
            'txt': 'fas fa-file-alt text-secondary',
            'xml': 'fas fa-code text-warning',
            'yml': 'fas fa-cog text-info',
            'yaml': 'fas fa-cog text-info',
            'c': 'fas fa-code text-primary',
            'cpp': 'fas fa-code text-primary',
            'h': 'fas fa-code text-info'
        };
        return iconMap[ext] || 'fas fa-file text-secondary';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    function toggleDirectory(element) {
        const ul = $(element).next('ul');
        const icon = $(element).find('i');
        
        if (ul.is(':visible')) {
            ul.hide();
            icon.removeClass('fa-folder-open').addClass('fa-folder');
        } else {
            ul.show();
            icon.removeClass('fa-folder').addClass('fa-folder-open');
        }
    }

    function selectFile(filePath, fileName, element) {
        // 更新选中状态
        $('.file-item').removeClass('selected');
        $(element).addClass('selected');
        
        selectedFile = filePath;
        $('#fileName').text(` - ${fileName}`);
        
        // 加载文件内容
        loadFileContent(filePath);
    }

    function loadFileContent(filePath) {
        $('#fileContent').html('<div class="loading"><i class="fas fa-spinner fa-spin"></i> 加载中...</div>');
        
        $.ajax({
            url: `/api/projects/${currentProject.project_id}/files/content`,
            method: 'GET',
            data: { path: filePath },
            success: function(response) {
                renderFileContent(response);
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                $('#fileContent').html(`<div class="text-danger p-3">加载失败: ${response.error || '未知错误'}</div>`);
            }
        });
    }

    function renderFileContent(fileData) {
        const { content, extension, size, path } = fileData;
        
        // 文件信息
        const fileInfo = `
            <div class="file-info">
                <strong>文件:</strong> ${path} | 
                <strong>大小:</strong> ${formatFileSize(size)} | 
                <strong>编码:</strong> ${fileData.encoding}
            </div>
        `;
        
        let contentHtml = '';
        
        // 根据文件类型渲染内容
        if (extension === '.md') {
            // Markdown文件
            contentHtml = `
                <div class="markdown-content">
                    ${marked.parse(content)}
                </div>
            `;
        } else if (extension === '.json') {
            // JSON文件
            try {
                const formatted = JSON.stringify(JSON.parse(content), null, 2);
                contentHtml = `<pre class="json-content">${escapeHtml(formatted)}</pre>`;
            } catch (e) {
                contentHtml = `<textarea class="code-editor" readonly>${content}</textarea>`;
            }
        } else {
            // 其他文本文件
            contentHtml = `<textarea class="code-editor" readonly>${content}</textarea>`;
        }
        
        $('#fileContent').html(fileInfo + contentHtml);
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function refreshFileTree() {
        loadFileTree();
        $('#fileContent').html('<div class="loading"><i class="fas fa-info-circle"></i> 请选择左侧文件进行预览</div>');
        $('#fileName').text('');
        selectedFile = null;
    }
</script>
{% endblock %}
