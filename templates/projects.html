{% extends "base.html" %}

{% block title %}项目管理 - AI任务管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">项目管理</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary" onclick="createProject()">
                <i class="fas fa-plus"></i> 新建项目
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshProjects()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

{% if projects %}
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 20%">项目名称</th>
                            <th style="width: 12%">创建时间</th>
                            <th style="width: 12%">更新时间</th>
                            <th style="width: 10%">LLM Provider</th>
                            <th style="width: 10%">任务类型</th>
                            <th style="width: 8%">任务数量</th>
                            <th style="width: 8%">已完成</th>
                            <th style="width: 20%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in projects %}
                        <tr class="project-row" data-project-id="{{ project.project_id }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-folder text-primary me-2"></i>
                                    <div>
                                        <div class="fw-bold">
                                            <a href="/project/{{project.project_id}}/tasks" class="text-decoration-none">
                                            {{ project.name }}
                                            </a>
                                        </div>
                                        {% if project.description %}
                                            <small class="text-muted">{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</small>
                                        {% else %}
                                            <small class="text-muted">暂无描述</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ project.created_at[:19].replace('T', ' ') }}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    {{ project.updated_at[:19].replace('T', ' ') }}
                                </small>
                            </td>
                            <td>
                                {% if project.provider == "local" %}
                                    <span class="badge bg-secondary">内网</span>
                                {% elif project.provider == "zhipu" %}
                                    <span class="badge bg-info text-dark">智谱</span>
                                {% elif project.provider == "claude" %}
                                    <span class="badge bg-primary">Claude</span>
                                {% else %}
                                    <span class="badge bg-light text-dark">{{ project.provider }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if project.task_type == "代码重构" %}
                                    <span class="badge bg-warning">代码重构</span>
                                {% elif project.task_type == "PMO需求" %}
                                    <span class="badge bg-info">PMO需求</span>
                                {% elif project.task_type == "新功能" %}
                                    <span class="badge bg-success">新功能</span>
                                {% elif project.task_type == "代码分析" %}
                                    <span class="badge bg-danger">代码分析</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ project.task_type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ project.total_tasks or 0 }}</span>
                            </td>
                            <td>
                                <span class="badge bg-success">{{ project.completed_tasks or 0 }}</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary"
                                            onclick="viewProjectDetail('{{ project.project_id }}')"
                                            title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="generateTasks('{{ project.project_id }}')"
                                            title="生成任务">
                                        <i class="fas fa-cogs"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info"
                                            onclick="viewTasks('{{ project.project_id }}')"
                                            title="任务列表">
                                        <i class="fas fa-tasks"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary"
                                            onclick="editProject('{{ project.project_id }}')"
                                            title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger"
                                            onclick="deleteProject('{{ project.project_id }}')"
                                            title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% else %}
    <div class="text-center py-5">
        <i class="fas fa-folder-open fa-5x text-muted mb-4"></i>
        <h3 class="text-muted">还没有项目</h3>
        <p class="text-muted mb-4">创建您的第一个项目来开始使用AI任务管理系统</p>
        <button class="btn btn-primary btn-lg" onclick="createProject()">
            <i class="fas fa-plus"></i> 创建项目
        </button>
    </div>
{% endif %}
{% endblock %}

{% block modals %}
<!-- 创建项目模态框 -->
<div class="modal fade" id="createProjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createProjectForm">
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectName" required>
                    </div>
                    <div class="mb-3">
                        <label for="projectWorkDir" class="form-label">工作目录 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectWorkDir" required>
                        <div class="form-text">项目文件将存储在此目录中</div>
                    </div>
                    <div class="mb-3">
                        <label for="projectProvider" class="form-label">LLM Provider</label>
                        <select class="form-control" id="projectProvider">
                            <option value="local">内网</option>
                            <option value="zhipu">智谱</option>
                            <option value="claude">Claude</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="projectTaskType" class="form-label">任务类型</label>
                        <select class="form-control" id="projectTaskType">
                            <option value="新功能">新功能</option>
                            <option value="代码重构">代码重构</option>
                            <option value="PMO需求">PMO需求</option>
                            <option value="代码分析">代码分析</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="projectRequirement" class="form-label">项目需求</label>
                        <textarea class="form-control" id="projectRequirement" rows="6"
                                  placeholder="支持Markdown语法"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="projectRulesConstraint" class="form-label">规则约束 (Markdown格式)</label>
                        <textarea class="form-control" id="projectRulesConstraint" rows="6"
                                  placeholder="支持Markdown语法，例如代码规范、架构约束等"></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            规则约束将被写入到项目目录的CLAUDE.md文件中
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitCreateProject()">创建项目</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑项目模态框 -->
<div class="modal fade" id="editProjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProjectForm">
                    <input type="hidden" id="editProjectId">
                    <div class="mb-3">
                        <label for="editProjectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editProjectName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectWorkDir" class="form-label">工作目录 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="editProjectWorkDir" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectProvider" class="form-label">LLM Provider</label>
                        <select class="form-control" id="editProjectProvider">
                            <option value="local">内网</option>
                            <option value="zhipu">智谱</option>
                            <option value="claude">Claude</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectTaskType" class="form-label">任务类型</label>
                        <select class="form-control" id="editProjectTaskType">
                            <option value="新功能">新功能</option>
                            <option value="代码重构">代码重构</option>
                            <option value="PMO需求">PMO需求</option>
                            <option value="代码分析">代码分析</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectRequirement" class="form-label">项目需求</label>
                        <textarea class="form-control" id="editProjectRequirement" rows="6"
                                  placeholder="支持Markdown语法"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editProjectRulesConstraint" class="form-label">规则约束 (Markdown格式)</label>
                        <textarea class="form-control" id="editProjectRulesConstraint" rows="6"
                                  placeholder="支持Markdown语法，例如代码规范、架构约束等"></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle"></i>
                            规则约束将被写入到项目目录的.claude/CLAUDE.md文件中
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitEditProject()">保存修改</button>
            </div>
        </div>
    </div>
</div>

<!-- 项目详情模态框 -->
<div class="modal fade" id="projectDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">项目详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="projectDetailContent">
                    <!-- 项目详情内容将通过JavaScript加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="editProjectFromDetail()">编辑项目</button>
            </div>
        </div>
    </div>
</div>

<!-- 生成任务确认模态框 -->
<div class="modal fade" id="generateTasksModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="generateTasksContent">
                    <!-- 内容将通过JavaScript加载 -->
                </div>
                <div class="mt-3" id="taskCountSection" style="display: none;">
                    <label for="taskCount" class="form-label">任务数量 (可选)</label>
                    <input type="number" class="form-control" id="taskCount" min="1" max="20"
                           placeholder="留空则自动确定任务数量">
                    <div class="form-text">
                        <i class="fas fa-info-circle"></i>
                        建议生成5-10个任务，留空将根据需求自动确定
                    </div>
                </div>
                <input type="hidden" id="generateTasksProjectId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGenerateTasks()">确认生成</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteProjectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个项目吗？此操作将同时删除项目下的所有任务。</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作不可撤销！
                </div>
                <input type="hidden" id="deleteProjectId">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteProject()">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshProjects() {
        location.reload();
    }
    
    // 自动填充工作目录
    $(document).ready(function() {
        $('#projectName').on('input', function() {
            const name = $(this).val().trim();
            if (name) {
                const workDir = `/tmp/projects/${name.replace(/\s+/g, '_')}`;
                $('#projectWorkDir').val(workDir);
            }
        });
    });
    
    function createProject() {
        // 显示创建项目模态框
        $('#createProjectModal').modal('show');

        // 自动填充工作目录
        $('#projectName').on('input', function() {
            const name = $(this).val().trim();
            if (name) {
                const workDir = `/tmp/projects/${name.replace(/\s+/g, '_')}`;
                $('#projectWorkDir').val(workDir);
            }
        });
    }
    
    function viewProjectDetail(projectId) {
        // 获取项目详情
        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'GET',
            success: function(project) {
                const content = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle"></i> 基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>项目名称:</strong></td><td>
                                        ${project.name}
                                    </td></tr>
                                <tr><td><strong>工作目录:</strong></td><td>${project.work_dir}</td></tr>
                                <tr><td><strong>创建时间:</strong></td><td>${project.created_at}</td></tr>
                                <tr><td><strong>更新时间:</strong></td><td>${project.updated_at}</td></tr>
                                <tr><td><strong>任务已生成:</strong></td><td>${project.tasks_generated ? '是' : '否'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-chart-bar"></i> 统计信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>总任务数:</strong></td><td><span class="badge bg-info">${project.total_tasks || 0}</span></td></tr>
                                <tr><td><strong>已完成:</strong></td><td><span class="badge bg-success">${project.completed_tasks || 0}</span></td></tr>
                                <tr><td><strong>完成率:</strong></td><td>${project.total_tasks > 0 ? Math.round((project.completed_tasks || 0) / project.total_tasks * 100) : 0}%</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6><i class="fas fa-file-alt"></i> 项目需求</h6>
                        <div class="border rounded p-3 bg-light">
                            ${project.requirement ? marked.parse(project.requirement) : '<em class="text-muted">暂无需求</em>'}
                        </div>
                    </div>
                `;
                $('#projectDetailContent').html(content);
                $('#projectDetailModal').modal('show');

                // 保存项目ID用于编辑
                $('#projectDetailModal').data('project-id', projectId);
            },
            error: function() {
                showAlert('获取项目信息失败', 'danger');
            }
        });
    }

    function editProjectFromDetail() {
        const projectId = $('#projectDetailModal').data('project-id');
        $('#projectDetailModal').modal('hide');
        setTimeout(() => editProject(projectId), 300);
    }

    function editProject(projectId) {
        // 获取项目信息
        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'GET',
            success: function(project) {
                // 填充表单
                $('#editProjectId').val(project.project_id);
                $('#editProjectName').val(project.name);
                $('#editProjectDescription').val(project.description || '');
                $('#editProjectWorkDir').val(project.work_dir);
                $('#editProjectRequirement').val(project.requirement || '');
                $('#editProjectProvider').val(project.provider || 'local');
                $('#editProjectTaskType').val(project.task_type || '新功能'); // 添加任务类型字段
                $('#editProjectRulesConstraint').val(project.rules_constraint || ''); 
                
                $('#editProjectModal').modal('show');
            },
            error: function() {
                showAlert('获取项目信息失败', 'danger');
            }
        });
    }

    function submitEditProject() {
        const projectId = $('#editProjectId').val();
        const name = $('#editProjectName').val().trim();
        const description = $('#editProjectDescription').val().trim();
        const workDir = $('#editProjectWorkDir').val().trim();
        const requirement = $('#editProjectRequirement').val().trim();
        const provider = $('#editProjectProvider').val();
        const taskType = $('#editProjectTaskType').val(); // 添加任务类型字段
        const rulesConstraint = $('#editProjectRulesConstraint').val().trim();

        if (!name || !workDir) {
            showAlert('项目名称和工作目录不能为空', 'warning');
            return;
        }

        // 显示加载状态
        $('#editProjectModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 保存中...');

        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify({
                name: name,
                description: description,
                work_dir: workDir,
                requirement: requirement,
                provider: provider,
                task_type: taskType, // 添加任务类型字段
                rules_constraint: rulesConstraint
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('项目更新成功！', 'success');
                    $('#editProjectModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('项目更新失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('项目更新失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                // 恢复按钮状态
                $('#editProjectModal .btn-primary').prop('disabled', false).html('保存修改');
            }
        });
    }
    
    function deleteProject(projectId) {
        $('#deleteProjectId').val(projectId);
        $('#deleteProjectModal').modal('show');
    }
    
    function confirmDeleteProject() {
        const projectId = $('#deleteProjectId').val();
        
        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showAlert('项目删除成功！', 'success');
                    $('#deleteProjectModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('项目删除失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('项目删除失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function generateTasks(projectId) {
        // 获取项目信息并显示生成任务确认
        $.ajax({
            url: `/api/projects/${projectId}`,
            method: 'GET',
            success: function(project) {
                let content = `<p>确定要为项目 <strong>${project.name}</strong> 生成任务吗？</p>`;

                if (!project.requirement || !project.requirement.trim()) {
                    content = `
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            项目 <strong>${project.name}</strong> 还没有设置需求，无法生成任务。
                        </div>
                        <p>请先编辑项目并添加需求内容。</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').hide();
                    $('#taskCountSection').hide();
                } else if (project.tasks_generated) {
                    content = `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            项目 <strong>${project.name}</strong> 已经生成过任务。
                        </div>
                        <p>重新生成将覆盖现有任务，确定要继续吗？</p>
                    `;
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                } else {
                    $('#generateTasksModal .modal-footer .btn-primary').show();
                    $('#taskCountSection').show();
                }

                $('#generateTasksContent').html(content);
                $('#generateTasksProjectId').val(projectId);
                $('#generateTasksModal').modal('show');
            },
            error: function() {
                showAlert('获取项目信息失败', 'danger');
            }
        });
    }

    function confirmGenerateTasks() {
        const projectId = $('#generateTasksProjectId').val();
        const taskCount = $('#taskCount').val();

        // 显示加载状态
        $('#generateTasksModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 生成中...');

        const requestData = {};
        if (taskCount && taskCount.trim()) {
            requestData.num_tasks = parseInt(taskCount);
        }

        $.ajax({
            url: `/api/projects/${projectId}/generate_tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                if (response.success) {
                    showAlert('任务生成成功！', 'success');
                    $('#generateTasksModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('任务生成失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                $('#generateTasksModal .btn-primary').prop('disabled', false).html('确认生成');
            }
        });
    }

    function viewTasks(projectId) {
        // 跳转到任务列表页面
        window.location.href = `/project/${projectId}/tasks`;
    }

    function submitCreateProject() {
        const name = $('#projectName').val().trim();
        const description = $('#projectDescription').val().trim();
        const workDir = $('#projectWorkDir').val().trim();
        const requirement = $('#projectRequirement').val().trim();
        const provider = $('#projectProvider').val();
        const taskType = $('#projectTaskType').val(); // 添加任务类型字段
        const rulesConstraint = $('#projectRulesConstraint').val().trim();        

        if (!name || !workDir) {
            showAlert('项目名称和工作目录不能为空', 'warning');
            return;
        }

        // 显示加载状态
        $('#createProjectModal .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 创建中...');

        $.ajax({
            url: '/api/projects',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                name: name,
                description: description,
                work_dir: workDir,
                requirement: requirement,
                provider: provider,
                task_type: taskType,
                rules_constraint: rulesConstraint
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('项目创建成功！', 'success');
                    $('#createProjectModal').modal('hide');
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    showAlert('项目创建失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('项目创建失败: ' + (response.message || '未知错误'), 'danger');
            },
            complete: function() {
                // 恢复按钮状态
                $('#createProjectModal .btn-primary').prop('disabled', false).html('创建项目');
            }
        });
    }

</script>
{% endblock %}
