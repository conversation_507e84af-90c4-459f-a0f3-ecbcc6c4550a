{% extends "base.html" %}

{% block title %}{{ requirement.title }} - 需求详情{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('projects') }}">项目</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('project_detail', project_id=project.project_id) }}">{{ project.name }}</a></li>
                <li class="breadcrumb-item active">{{ requirement.title }}</li>
            </ol>
        </nav>
        <h1 class="h2">{{ requirement.title }}</h1>
        <p class="text-muted">{{ requirement.description }}</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if not requirement.tasks_generated %}
            <button type="button" class="btn btn-primary" onclick="generateTasks()">
                <i class="fas fa-cogs"></i> 生成任务
            </button>
            {% endif %}
            {% if requirement.tasks_generated %}
            <button type="button" class="btn btn-success" onclick="runTasks()" id="runTasksBtn">
                <i class="fas fa-play"></i> 运行任务
            </button>
            <button type="button" class="btn btn-warning" onclick="stopTasks()" id="stopTasksBtn" style="display: none;">
                <i class="fas fa-stop"></i> 停止任务
            </button>
            <a href="{{ url_for('task_management', req_id=requirement.req_id) }}" class="btn btn-info">
                <i class="fas fa-tasks"></i> 任务管理
            </a>
            <a href="{{ url_for('task_execution', req_id=requirement.req_id) }}" class="btn btn-outline-info">
                <i class="fas fa-play-circle"></i> 执行管理
            </a>
            {% endif %}
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 需求信息 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">需求信息</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>需求ID:</strong> {{ requirement.req_id }}</p>
                        <p><strong>状态:</strong> {{ getStatusBadge(requirement.status)|safe }}</p>
                        <p><strong>优先级:</strong> {{ getPriorityBadge(requirement.priority)|safe }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>创建时间:</strong> {{ formatDateTime(requirement.created_at) }}</p>
                        <p><strong>更新时间:</strong> {{ formatDateTime(requirement.updated_at) }}</p>
                        <p><strong>任务已生成:</strong> 
                            {% if requirement.tasks_generated %}
                                <span class="badge bg-success">是</span>
                            {% else %}
                                <span class="badge bg-secondary">否</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">任务状态</h5>
            </div>
            <div class="card-body" id="task-status-card">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status" id="status-loading" style="display: none;">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div id="status-content">
                        <p class="text-muted">暂无运行中的任务</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务列表 -->
{% if tasks %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">任务列表 ({{ tasks|length }})</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="resetAllTasks()">
                <i class="fas fa-undo"></i> 重置所有
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>标题</th>
                        <th>状态</th>
                        <th>优先级</th>
                        <th>依赖</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>{{ task.id }}</td>
                        <td>
                            <strong>{{ task.title }}</strong>
                            <br><small class="text-muted">{{ task.description[:50] }}{% if task.description|length > 50 %}...{% endif %}</small>
                        </td>
                        <td>{{ getStatusBadge(task.status)|safe }}</td>
                        <td>{{ getPriorityBadge(task.priority)|safe }}</td>
                        <td>
                            {% if task.dependencies %}
                                {% for dep in task.dependencies %}
                                    <span class="badge bg-info">{{ dep }}</span>
                                {% endfor %}
                            {% else %}
                                <span class="text-muted">无</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="viewTask({{ task.id }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if task.status == 'pending' %}
                                <button class="btn btn-outline-success" onclick="runSingleTask({{ task.id }})">
                                    <i class="fas fa-play"></i>
                                </button>
                                {% endif %}
                                {% if task.status in ['completed', 'failed'] %}
                                <button class="btn btn-outline-warning" onclick="resetTask({{ task.id }})">
                                    <i class="fas fa-undo"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- 执行日志 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">执行日志</h5>
        <div class="btn-group btn-group-sm">
            <button class="btn btn-outline-secondary" onclick="refreshLogs()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
            <button class="btn btn-outline-danger" onclick="clearLogs()">
                <i class="fas fa-trash"></i> 清空
            </button>
        </div>
    </div>
    <div class="card-body">
        <div class="log-container" id="log-container">
            {% if logs %}
                {% for log in logs %}
                <div class="log-entry log-level-{{ log.level }}">
                    <small class="text-muted">{{ log.timestamp[:19] }}</small>
                    <span class="badge bg-secondary">{{ log.event_type }}</span>
                    {% if log.task_id %}
                        <span class="badge bg-info">Task-{{ log.task_id }}</span>
                    {% endif %}
                    <span class="log-level-{{ log.level }}">{{ log.message }}</span>
                </div>
                {% endfor %}
            {% else %}
                <p class="text-muted text-center">暂无日志</p>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 任务详情模态框 -->
<div class="modal fade" id="taskDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">任务详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="task-detail-content">
                <!-- 任务详情内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 生成任务模态框 -->
<div class="modal fade" id="generateTasksModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">生成任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>为这个需求生成AI任务？</p>
                <div class="mb-3">
                    <label for="numTasks" class="form-label">任务数量（可选）</label>
                    <input type="number" class="form-control" id="numTasks" min="1" max="20" 
                           placeholder="留空让AI自动决定">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmGenerateTasks()">生成任务</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let statusCheckInterval;

    $(document).ready(function() {
        // 检查任务状态
        checkTaskStatus();

        // 定期检查任务状态
        statusCheckInterval = setInterval(checkTaskStatus, 5000);
    });

    function checkTaskStatus() {
        $.ajax({
            url: `/api/requirements/{{ requirement.req_id }}/task_status`,
            method: 'GET',
            success: function(status) {
                updateTaskStatusDisplay(status);
            },
            error: function() {
                // 忽略错误，继续检查
            }
        });
    }

    function updateTaskStatusDisplay(status) {
        const statusContent = $('#status-content');
        const runBtn = $('#runTasksBtn');
        const stopBtn = $('#stopTasksBtn');

        if (status.status === 'running') {
            statusContent.html(`
                <div class="text-center">
                    <div class="spinner-border text-primary mb-2" role="status"></div>
                    <p class="mb-0">任务运行中...</p>
                    <small class="text-muted">开始时间: ${formatDateTime(status.start_time)}</small>
                </div>
            `);
            runBtn.hide();
            stopBtn.show();
        } else if (status.status === 'completed') {
            statusContent.html(`
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-2x mb-2"></i>
                    <p class="mb-0">任务已完成</p>
                    <small class="text-muted">完成时间: ${formatDateTime(status.end_time)}</small>
                </div>
            `);
            runBtn.show();
            stopBtn.hide();
            // 刷新页面以显示最新结果
            setTimeout(() => location.reload(), 2000);
        } else if (status.status === 'failed') {
            statusContent.html(`
                <div class="text-center">
                    <i class="fas fa-exclamation-circle text-danger fa-2x mb-2"></i>
                    <p class="mb-0">任务执行失败</p>
                    <small class="text-muted">结束时间: ${formatDateTime(status.end_time)}</small>
                </div>
            `);
            runBtn.show();
            stopBtn.hide();
        } else if (status.status === 'stopped') {
            statusContent.html(`
                <div class="text-center">
                    <i class="fas fa-stop-circle text-warning fa-2x mb-2"></i>
                    <p class="mb-0">任务已停止</p>
                    <small class="text-muted">停止时间: ${formatDateTime(status.end_time)}</small>
                </div>
            `);
            runBtn.show();
            stopBtn.hide();
        } else {
            statusContent.html(`
                <div class="text-center">
                    <i class="fas fa-clock text-muted fa-2x mb-2"></i>
                    <p class="mb-0">暂无运行中的任务</p>
                </div>
            `);
            runBtn.show();
            stopBtn.hide();
        }
    }

    function generateTasks() {
        $('#generateTasksModal').modal('show');
    }

    function confirmGenerateTasks() {
        const numTasks = $('#numTasks').val() || null;

        showAlert('正在生成任务，请稍候...', 'info');
        $('#generateTasksModal').modal('hide');

        $.ajax({
            url: `/api/requirements/{{ requirement.req_id }}/generate_tasks`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                num_tasks: numTasks ? parseInt(numTasks) : null
            }),
            success: function(response) {
                if (response.success) {
                    showAlert('任务生成成功！', 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert('任务生成失败: ' + response.message, 'danger');
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON || {};
                showAlert('任务生成失败: ' + (response.message || '未知错误'), 'danger');
            }
        });
    }

    function runTasks() {
        if (confirm('确定要运行所有任务吗？')) {
            showAlert('任务开始运行，请稍候...', 'info');

            $.ajax({
                url: `/api/requirements/{{ requirement.req_id }}/run_tasks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    parallel_mode: true
                }),
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已开始运行！', 'success');
                    } else {
                        showAlert('任务运行失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('任务运行失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function stopTasks() {
        if (confirm('确定要停止任务执行吗？')) {
            $.ajax({
                url: `/api/requirements/{{ requirement.req_id }}/stop_tasks`,
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        showAlert('任务已停止！', 'warning');
                    } else {
                        showAlert('停止任务失败: ' + response.message, 'danger');
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON || {};
                    showAlert('停止任务失败: ' + (response.message || '未知错误'), 'danger');
                }
            });
        }
    }

    function viewTask(taskId) {
        // 查找任务详情
        const tasks = {{ tasks|tojson|safe }};
        const task = tasks.find(t => t.id === taskId);

        if (task) {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>任务ID:</strong> ${task.id}</p>
                        <p><strong>标题:</strong> ${task.title}</p>
                        <p><strong>状态:</strong> ${getStatusBadge(task.status)}</p>
                        <p><strong>优先级:</strong> ${getPriorityBadge(task.priority)}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>依赖:</strong> ${task.dependencies.length ? task.dependencies.join(', ') : '无'}</p>
                        <p><strong>测试策略:</strong> ${task.testStrategy || '无'}</p>
                    </div>
                </div>
                <div class="mb-3">
                    <strong>描述:</strong>
                    <p>${task.description}</p>
                </div>
                <div class="mb-3">
                    <strong>详细信息:</strong>
                    <pre class="bg-light p-3">${task.details || '无'}</pre>
                </div>
                ${task.result ? `
                <div class="mb-3">
                    <strong>执行结果:</strong>
                    <pre class="bg-light p-3">${task.result}</pre>
                </div>
                ` : ''}
            `;

            $('#task-detail-content').html(content);
            $('#taskDetailModal').modal('show');
        }
    }

    function runSingleTask(taskId) {
        if (confirm(`确定要运行任务 ${taskId} 吗？`)) {
            showAlert(`任务 ${taskId} 开始运行...`, 'info');
            // 这里可以实现单个任务运行的API
            // 暂时显示提示信息
            showAlert('单个任务运行功能正在开发中...', 'warning');
        }
    }

    function resetTask(taskId) {
        if (confirm(`确定要重置任务 ${taskId} 的状态吗？`)) {
            showAlert('任务状态重置功能正在开发中...', 'warning');
        }
    }

    function resetAllTasks() {
        if (confirm('确定要重置所有任务的状态吗？')) {
            showAlert('批量重置功能正在开发中...', 'warning');
        }
    }

    function refreshLogs() {
        location.reload();
    }

    function clearLogs() {
        if (confirm('确定要清空所有日志吗？')) {
            showAlert('清空日志功能正在开发中...', 'warning');
        }
    }

    // 页面卸载时清除定时器
    $(window).on('beforeunload', function() {
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
        }
    });
</script>
{% endblock %}
