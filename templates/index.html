{% extends "base.html" %}

{% block title %}仪表板 - AI任务管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">仪表板</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> 刷新
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            项目总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-projects">
                            {{ projects|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-folder fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            活跃项目
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-projects">
                            {{ projects|selectattr('requirements')|list|length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-play fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总需求数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-requirements">
                            {% set total_reqs = projects|map(attribute='requirements')|map('length')|sum %}
                            {{ total_reqs }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            运行中任务
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800" id="running-tasks">
                            0
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cog fa-spin fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近项目 -->
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最近项目</h6>
                <a href="{{ url_for('projects') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if projects %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>需求数量</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects[:5] %}
                                <tr>
                                    <td>
                                        <a href="/project/{{project.project_id}}/tasks" class="text-decoration-none">
                                            <strong>{{ project.name }}</strong>
                                        </a>
                                        {% if project.description %}
                                            <br><small class="text-muted">{{ project.description[:50] }}{% if project.description|length > 50 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ project.requirements|length }}</span>
                                    </td>
                                    <td>{{ project.created_at[:10] }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">还没有项目，<a href="#" onclick="createProject()">创建第一个项目</a></p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" onclick="createProject()">
                        <i class="fas fa-plus"></i> 新建项目
                    </button>
                    <button class="btn btn-outline-secondary" onclick="showQuickTask()">
                        <i class="fas fa-bolt"></i> 快速任务
                    </button>
                    <a href="{{ url_for('projects') }}" class="btn btn-outline-info">
                        <i class="fas fa-list"></i> 浏览项目
                    </a>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统状态</h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>系统运行状态</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Claude连接</span>
                        <span class="badge bg-success">已连接</span>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>数据存储</span>
                        <span class="badge bg-success">正常</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block modals %}
<!-- 快速任务模态框 -->
<div class="modal fade" id="quickTaskModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">快速任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickTaskForm">
                    <div class="mb-3">
                        <label for="quickTaskDescription" class="form-label">任务描述</label>
                        <textarea class="form-control" id="quickTaskDescription" rows="3" 
                                placeholder="请描述您想要完成的任务..."></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="quickTaskProject" class="form-label">选择项目</label>
                        <select class="form-select" id="quickTaskProject">
                            <option value="">创建新项目</option>
                            {% for project in projects %}
                            <option value="{{ project.project_id }}">{{ project.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="executeQuickTask()">开始执行</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshDashboard() {
        location.reload();
    }
    
    function showQuickTask() {
        $('#quickTaskModal').modal('show');
    }
    
    function executeQuickTask() {
        const description = $('#quickTaskDescription').val().trim();
        if (!description) {
            showAlert('请输入任务描述', 'warning');
            return;
        }
        
        // 这里实现快速任务逻辑
        showAlert('快速任务功能正在开发中...', 'info');
        $('#quickTaskModal').modal('hide');
    }
    
    // 页面加载完成后更新运行中任务数量
    $(document).ready(function() {
        updateRunningTasksCount();
    });
    
    function updateRunningTasksCount() {
        // 这里可以通过API获取运行中的任务数量
        // 暂时显示0
        $('#running-tasks').text('0');
    }
</script>
{% endblock %}
