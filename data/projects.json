{"projects": [{"project_id": "fd23027e-aaf1-4d2b-ac63-ce93c0d5a354", "name": "俄罗斯方块", "work_dir": "/mnt/d/agent/auto-claude-tasks/demo", "description": "", "requirement": "使用html生成一个俄罗斯方块游戏", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-09-25T20:29:17.275019", "updated_at": "2025-09-30T16:55:01.289492", "tasks_generated": true, "task_manager_session": "89c0d7db-146b-4f7a-b997-226bbec93196"}, {"project_id": "1758977931726", "name": "csdk项目C语言重构", "work_dir": "/mnt/d/aicode/csdkc/", "description": "csdk项目C语言重构", "requirement": "# 需求\n 1. 使用纯C语言重构当前项目的src目录下的所有cpp代码\n 2. 重构完成后，修改CMakeLists.txt文件，使用纯C语言的源文件目录编译，在build目录使用cmake -DWITH_TESTS=ON ..编译确保编译成功\n 3. 并运行自动化测试：cd build/tests && ./auto_CCSP 1，分析测试失败的用例，并解决\n# 核心思路\n 1. 先让代码能“以 C 的方式编译”，\n 2. 再逐步把 C++ 专属实现换成 C 实现，\n 3. 最后把 CMake 侧彻底改成 C 工程。\n\n# 增量迭代修改\n逐个修改src目录下的.cpp及相关的.h，按照按照规则要求重构为C语言实现。\n每修改完一个.cpp文件后，修改CMakefile把修改后的.c包含进来，编译通过后，在build/tests目录下，运行./auto_CCSP 1，确保所有没有“测试失败”的用例。", "provider": "local", "rules_constraint": "## “C++ 语法”变成“C 语法”的规则\n1.文件改名\n把src/目录下的所有 .cpp/.cxx/.cc全部改成 .c，头文件如果里面只有宏/声明可以保持 .h。\n2. 去掉 extern \"C\"\n原来为了 C/C++ 混编写的全部删掉，只剩纯 C 声明。\n3.把 C++ 关键字替换掉\n - bool → C99 <stdbool.h> 的 bool\n - true/false → 同上\n - new/delete → malloc/free（或自己写的对象池） \n - 把 class/struct 混用的地方全部改成 struct；成员函数指针改成“前置声明 + 全局函数”或者“把 this 显式当第一个参数”。\n \n4. 把 C++ 标准库替换成 C 等价物，示例如下：\n- `std::string` :  直接 `char *` + 长度，或者自己写 `struct string { char *data; size_t len; }`\n- `std::vector<T>`: 宏模板“伪泛型” (utarray, utlist 等),或者 `T *arr; size_t cap; size_t len;`\n- `std::map/`unordered\\_map ： `uthash`（单头文件哈希）\n- `std::shared_ptr`: 引用计数裸指针：`struct obj { int ref; ... };`\n- `std::exception`: 返回错误码 `int/errno`<br>, `jmp_buf/longjmp` 做非局部跳转 \n- `std::thread`:`pthread.h` (`pthread_create/join`)\n- `std::mutex`: `pthread_mutex_t`\n- `iostream`: `stdio.h`\n5.把函数重载改掉：用“函数名前缀”或“带后缀”的方式\n6.把名字空间改成前缀\n7.模板/泛型，如果原来用了简单模板（如 vector<int> 、 vector<double> ）, 可直接写死两份如： int_array_t、double_array_t\n## 代码复用要求\n重构过程中，需要检查是否已经有类似的代码可以复用，如：int_array_t、struct定义等。\n## 文件重构的范围\n只重构src/目录下的cpp代码。", "task_type": "代码重构", "created_at": "2025-09-27T20:58:51.730570", "updated_at": "2025-09-29T23:48:31.544936", "tasks_generated": true, "task_manager_session": "dbd7c678-8236-4d46-a391-b4d30e84b3b0"}, {"project_id": "1759193419029", "name": "禅道项目重构", "work_dir": "/mnt/d/aicode/zentaopms", "description": "", "requirement": "# 项目需求\n1. 详细分析分析当前 PHP 项目的目录结构、MVC 分层、路由机制、数据库模型和依赖关系。\n2. 根据当前项目的全部功能，将所有功能模块重构为java语言.\n3. 重构完成，编译java项目，并运行、验证所有功能已经正确重构完成。", "provider": "claude", "rules_constraint": "## 项目背景\n我正在进行当前项目的重构，原项目使用PHP语言，请按照Spring Boot最佳实践进行重构。\n技术栈：后端使用springboot,前端使用bootstrap5+jquery+ajax实现\n\n## 重构目标：\n- 功能对等迁移，不新增功能\n- 保持API接口兼容性\n- 采用分层架构：Controller-Service-Repository\n- 使用Spring Data JPA进行数据访问\n\n## 代码转换指导原则\n- PHP关联数组 → Java Map或DTO对象\n- PHP类属性 → Java私有字段 + Getter/Setter\n- PHP include/require → Spring依赖注入\n- PHP session → Spring Session或Redis\n- PHP mysqli → Spring Data JPA\n\n## 架构模式指导\n- 单一职责原则：每个类只负责一个明确的功能\n- 依赖倒置：通过接口抽象依赖关系\n- 使用DTO进行层间数据传输\n- 业务逻辑集中在Service层\n- 数据访问通过Repository接口\n- 控制器保持轻量，只处理HTTP相关逻辑\n\n\n## 重构后的java项目目录\n/mnt/d/aicode/zentaopms/javapms/zentao-java\n\n## 当前项目的核心架构\n- **framework/**: Core framework classes (control, model, router, helper)\n- **module/**: Modular architecture with each module containing:\n  - `control.php` - Controller logic\n  - `model.php` - Data layer and business logic\n  - `zen.php` - New architecture layer (when present)\n  - `tao.php` - Extended business logic layer (when present)\n  - `config/` - Module-specific configuration\n  - `lang/` - Internationalization files\n  - `view/` - Traditional view templates\n  - `ui/` - Modern UI components\n  - `css/` and `js/` - Frontend assets\n- **lib/**: Third-party libraries and utility classes\n- **config/**: Global configuration files\n- **www/**: Web entry point and public assets\n- **db/**: Database schemas and migration scripts\n- **extension/**: Extension system for customization", "task_type": "代码重构", "created_at": "2025-09-30T08:50:19.031700", "updated_at": "2025-09-30T08:50:19.031700", "tasks_generated": false, "task_manager_session": null}, {"project_id": "1759223401732", "name": "测试项目(更新)", "work_dir": "/tmp/test_project", "description": "这是一个更新后的测试项目", "requirement": "实现一个简单的功能", "provider": "local", "rules_constraint": "", "task_type": "新功能", "created_at": "2025-09-30T17:10:01.732320", "updated_at": "2025-09-30T17:15:25.220631", "tasks_generated": true, "task_manager_session": "5c6873b2-faba-4eaf-8509-8d61022857c5"}], "meta": {"total_projects": 4, "last_updated": "2025-09-30T17:15:25.221147"}}